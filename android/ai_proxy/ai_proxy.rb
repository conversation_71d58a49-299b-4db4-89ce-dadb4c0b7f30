require 'socket'
require 'timeout'
require 'browserstack_logger'
require 'android_toolkit'
require 'json'

require_relative '../helpers/utils'
require_relative '../constants'
require_relative '../../common/push_to_zombie'

class AIProxy
  NODE_PATH = "#{ENV['NODE_20_PATH']}/bin/node".freeze
  AI_PROXY_START_TIMEOUT = 15
  AI_PROXY_START_MAX_TIMEOUT = 15

  AI_PROXY_CONFIG = {
    "ai_proxy_path" => "#{BrowserStack::BS_DIR}/deps/automate-ai-proxy/v1.2", #maintain version here
    "log_path_wo_device" => "#{BrowserStack::LOG_DIR_PATH}/ai-proxy"
  }.freeze

  def initialize(params, ai_proxy_port, selenium_port, device)
    @request_params = params
    @device = device
    @session_id = params["automate_session_id"]
    @ai_proxy_port = ai_proxy_port
    @selenium_port = selenium_port
    @terminal_ip = @request_params[:terminal_ip]
    @product = !params[:genre].nil? && params[:genre].to_s.eql?('app_automate') ? "app-automate" : "automate"
    @device_name = params[:deviceName]
    @device_version = params["version"]

    @ai_proxy_upstream = "http://localhost:#{@selenium_port}"
    @ai_proxy_log_path = "#{AI_PROXY_CONFIG['log_path_wo_device']}_#{@device}.log"

    BrowserStack.logger.info(
      "Setting AI Proxy Android properties: "\
      "@request_params #{@request_params}, "\
      "@device #{@device}, "\
      "@ai_proxy_port #{@ai_proxy_port}, "\
      "@terminal_ip #{@terminal_ip}, "\
      "@selenium_port #{@selenium_port}, "\
      "@ai_proxy_log_path #{@ai_proxy_log_path} "\
    )
  end

  def start_ai_proxy
    if ai_proxy_running?
      BrowserStack.logger.info "AI Proxy is already running at #{@ai_proxy_port}, killing it"
      kill_ai_proxy(@ai_proxy_port, @terminal_ip)
    else
      BrowserStack.logger.info "AI Proxy is not running at #{@ai_proxy_port}"
    end

    system("sudo chmod 666 #{AI_PROXY_CONFIG['ai_proxy_path']}/lib/config/config.json")
    ai_proxy_config_path = "#{AI_PROXY_CONFIG['ai_proxy_path']}/lib/config/config.json"
    ai_proxy_config_json_data = set_ai_proxy_config(
      ai_proxy_config_path,
      @terminal_ip,
      @selenium_port,
      @ai_proxy_upstream,
      @ai_proxy_port,
      @device,
      @device_name,
      @device_version,
      @product,
      @request_params
    )
    BrowserStack.logger.info "Setting ai_proxy_config_json_data as #{ai_proxy_config_json_data}"

    pid = Process.fork do
      command = "NODE_ENV=prod "\
      "PORT=#{@ai_proxy_port} "\
      "UPSTREAM=#{@ai_proxy_upstream} "\
      "HOST=#{@terminal_ip} "\
      "ADB=#{BrowserStack::ADB} "\
      "#{NODE_PATH} #{AI_PROXY_CONFIG['ai_proxy_path']}/cluster.js > #{@ai_proxy_log_path} 2>&1"
      BrowserStack.logger.info "Starting AI Proxy with the command #{command}"
      system(command)
    end
    Process.detach(pid)

    Timeout.timeout(AI_PROXY_START_TIMEOUT) do
      AI_PROXY_START_MAX_TIMEOUT.times do |attempt|
        sleep(1)
        if ai_proxy_running?
          BrowserStack.logger.info(
            "AI proxy running on port #{@ai_proxy_port}"
          )
          return true
        else
          BrowserStack.logger.info(
            "Attempt to start AI proxy: #{attempt}"
          )
        end
      end
    end
  rescue Timeout::Error => e
    BrowserStack.logger.info "Timeout unable to start AI proxy for session_id :#{@session_id} - #{e.message}"
    push_to_zombie(
      'start_ai_proxy',
      'Timeout unable to start AI proxy',
      { "session_id" => @session_id }
    )
    false
  rescue StandardError => e
    BrowserStack.logger.info "Some Error occurred while in start_ai_proxy: "\
    "#{e.inspect} #{e.message} #{e.backtrace.join("\n")}"
    push_to_zombie(
      'start_ai_proxy',
      'Some Error occurred while in start_ai_proxy',
      { "session_id" => @session_id, "message" => e.message, "backtrace" => e.backtrace.join("\n") }
    )
    false
  end

  def push_to_zombie(kind: '', error: '', data: '')
    zombie_key_value(
      platform: 'android',
      kind: kind,
      error: error,
      device: @device,
      data: data
    )
  end

  def ai_proxy_running?
    cmd = "lsof -i -P -n | grep node | grep #{@ai_proxy_port} | "\
    "grep #{@terminal_ip}:#{@ai_proxy_port} | awk '{print $2}'"
    ai_proxy_pids = `#{cmd}`
    return true if !ai_proxy_pids.nil? && ai_proxy_pids != ""

    false
  end
end
