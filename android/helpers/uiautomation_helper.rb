require 'android_toolkit'
require 'logger'
require_relative 'popup_helper'
require_relative '../models/android_device'
require_relative '../helpers/cleanup_function_runner'
require_relative './ui_automation_rules_helper'
require '/usr/local/.browserstack/mobile/common/push_to_zombie'
require_relative 'utils'

module BrowserStack
  class UiAutomationHelper
    # Only UiAutomation app is supported
    # The jar is not supported

    RUNNER_CLASS_NAME = "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner".freeze
    DEFAULT_TIMEOUT = 25
    LOG_FILE = File.join(BrowserStack::LOGGING_DIR, "ui_automation.log")

    def initialize(device_id, logger, logger_params = {})
      @device_id = device_id
      @logger = logger
      @cleanup_function_runner = CleanupFunctionRunner.new(@device_id, @logger)
      @logger_params = logger_params
    end

    def handle_location_popups(os_version)
      log(:info, "Handling location popups")
      if os_version.to_i >= 11
        output = start('com.browserstack.uiautomation.DisableLocationPopupTest', 40)
        log(:info, "Disabling play store auto updates (version 11 and above)")
        res = start('com.browserstack.uiautomation.GooglePlayStoreDisableUpdatesTest', 40)
      else
        output = run_ui_automation("PopupHandlerAutomation.jar",
                                   "com.browserstack.popupHandler.DisableLocationPopup")
        log(:info, "Disabling play store auto updates (below version 11)")
        res = run_ui_automation("/data/local/tmp/google-play-store-login.jar",
                                "com.browserstack.googleplaystore.GooglePlayStoreDisableUpdates")
      end
      instrument_automation("Disable_Location_Popup_Test", output, @device_id)
      instrument_automation("Google_PlayStore_Disable_Updates_Test", res, @device_id)
    end

    def handle_playstore_updates(os_version)
      if os_version.to_i >= 11
        log(:info, "Disabling play store auto updates (version 11 and above)")
        start('com.browserstack.uiautomation.GooglePlayStoreDisableUpdatesTest', 40)
      else
        log(:info, "Disabling play store auto updates (below version 11)")
        run_ui_automation("/data/local/tmp/google-play-store-login.jar",
                          "com.browserstack.googleplaystore.GooglePlayStoreDisableUpdates")
      end
    end

    def handle_huawei_device_owner_popup(_os_version = nil)
      log(:info, 'Running automation to handle Huawei security popup')

      5.times do
        if current_focus.match?('Security threat')
          log(:info, 'Security threat popup found, running automation')
          start('com.browserstack.uiautomation.HuaweiDeviceOwnerPopupTest')
          break unless current_focus.match?('Security threat')
        end

        log(:info, 'Security threat popup not found, sleeping')
        sleep 1
      end
    end

    def handle_oneplus_welcome_screen(_os_version = nil)
      2.times do
        next unless current_focus.include?('com.coloros.bootreg')

        log(:info, 'OnePlus device is on welcome screen after factory reset')
        if current_focus.include?('com.coloros.bootreg.settings.activity.GuidePage')
          # unable to dismiss this screen via ui automation,
          # "ERROR: could not get idle state." is thrown on adb shell uiautomator dump
          log(:info, 'Tapping to get rid of first screen')
          adb.shell('input tap 620 2100')
        end
        start('com.browserstack.uiautomation.OneplusWelcomeScreenDismiss')
      end
    end

    def dismiss_setup_wizard_popup(_os_version = nil)
      # A pop-up appears to select the home app. Tapping here on samsung setup wizard option.
      # This appears only in reboot that follows on enabling testharness. Hence the reason check.
      # Sleep is to ensure preceeding browser checks are done and the popup appears again on home.
      # Device is in landscape mode sometimes so orientation needs to be checked- MOBFR-364
      return unless @cleanup_function_runner.should_run?("dismiss_setup_wizard_popup")

      log(:info, "Running automation to dismiss setup wizard popup")
      sleep(5)
      adb.shell('input tap 850 570')
      adb.shell('input tap 780 650')
      adb.shell('input tap 516 1323')
      adb.shell('input tap 516 1475')
    end

    def handle_vpn_auth_dialog(os_version)
      tries = 6
      log(:info, "Handling vpn auth dialog for os #{os_version}")
      while tries > 0
        output = if os_version.to_i >= 11
                   start('com.browserstack.uiautomation.VpnAuthTest')
                 else
                   adb.push(BrowserStack::USB_VPN_JAR, BrowserStack::DATA_LOCAL_TMP)
                   run_ui_automation("VpnAuth.jar", "com.bs.vpnauth.VpnAuth", 20)
                 end
        log(:info, "Output for handle_vpn_auth_dialog #{output}")
        if output.match(/VPN auth dialog automation successful/)
          adb.shell('input keyevent 3')
          log(:info, "Vpn auth handled successfully")
          break
        else
          sleep 0.5
          tries -= 1
          log(:info, "Vpn auth failed. Tries left: #{tries}")
          if tries == 0
            adb.shell('input keyevent 3')
            raise "VpnAuthFailed"
          end
        end
      end
    end

    def revoke_android_intelligence_permission(os_version)
      device = AndroidDevice.new(@device_id, "Cleanup", @logger)
      automation_config_name = "revoke_android_intelligence_permissions_rule"

      if device.manufacturer == "Google" && device.os_version.to_i >= 14
        log(:info, "Handling revoke permissions popups for pixel 8 and above")
        toggle_via, intent = BrowserStack::UiAutomationRulesHelper.get_rule_for_automation(@device_id,
                                                                                           automation_config_name)
        if toggle_via == "intent_automation"
          log(:info, "Running revoke_android_intelligence_permission via Intent")
          adb.shell('am force-stop com.android.settings')
          adb.shell("am start #{intent}")
        end
        output = start('com.browserstack.uiautomation.AndroidIntelligencePermissionRevokeTestPixel8Above', 50)
        log(:info, "Output for revoke_android_intelligence_permission for Pixel 8 and above #{output}")
        if output.include?("OK (1 test)")
          log(:info, "Android Intelligence Permission revoked successfully for Pixel 8 and above")
          push_to_zombie_uiautomation(@device_id, "Android Intelligence Permission Revoked for Pixel 8 and above")
        else
          log(:info, "Android Intelligence Permission not revoked successfully for Pixel 8 and above")
          push_to_zombie_uiautomation(@device_id, "Android Intelligence Permission Not Revoked for Pixel 8 and above")
        end
        return
      end

      log(:info, "Handling revoke permissions popups")
      if os_version.to_i >= 11
        toggle_via, intent = BrowserStack::UiAutomationRulesHelper.get_rule_for_automation(@device_id,
                                                                                           automation_config_name)
        if toggle_via == "intent_automation"
          log(:info, "Running revoke_android_intelligence_permission via Intent")
          adb.shell('am force-stop com.android.settings')
          adb.shell("am start #{intent}")
        end
      end
      output = if os_version.to_i >= 11
                 start('com.browserstack.uiautomation.AndroidIntelligencePermissionRevokeTest', 50)
               end
      log(:info, "Output for revoke_android_intelligence_permission #{output}")
      if output.include?("OK (1 test)")
        log(:info, "Android Intelligence Permission revoked successfully")
        push_to_zombie_uiautomation(@device_id, "Android Intelligence Permission Revoked")
      else
        log(:info, "Android Intelligence Permission not revoked successfully")
        push_to_zombie_uiautomation(@device_id, "Android Intelligence Permission Not Revoked")
      end
    end

    def allow_home_screen_rotation_enabled(_os_version = nil, max_retries = 3)
      log(:info, "Enabling Home Screen Rotation")
      success = false
      max_retries.times do |attempt|
        output = start('com.browserstack.uiautomation.AllowHomeScreenRotationTest', 30)
        log(:info, "Output for allow_home_screen_rotation_enabled attempt #{attempt + 1} : #{output}")
        if output.include?("OK (1 test)")
          log(:info, "Home Screen Rotation enabled successfully")
          success = true
          push_to_zombie_uiautomation(@device_id, "Home Screen Rotation Enabled", (attempt + 1).to_s)
          break
        end
        log(:warn, "Retrying... Attempt #{attempt + 1} of #{max_retries}") if attempt < max_retries - 1
      end
      unless success
        log(:error, "Failed to enable Home Screen Rotation after #{max_retries} attempts")
        push_to_zombie_uiautomation(@device_id, "Home Screen Rotation NOT Enabled")
      end
      adb.shell('am force-stop com.android.settings')
    end

    def ensure_google_location_accuracy_enabled(_os_version = nil, max_retries = 3)
      log(:info, "Enabling Google Location Accuracy")
      success = false
      max_retries.times do |attempt|
        adb.shell('am force-stop com.android.settings')
        output = start('com.browserstack.uiautomation.EnableGoogleLocationAccuracyTest', 30)
        log(:info, "Output for ensure_google_location_accuracy_enabled attempt #{attempt + 1} : #{output}")
        if output.include?("OK (1 test)")
          log(:info, "Google Location Accuracy enabled successfully")
          success = true
          push_to_zombie_uiautomation(@device_id, "Google Location Accuracy Enabled", (attempt + 1).to_s)
          break
        end
        log(:warn, "Retrying... Attempt #{attempt + 1} of #{max_retries}") if attempt < max_retries - 1
      end
      unless success
        log(:error, "Failed to enable Google Location Accuracy after #{max_retries} attempts")
        push_to_zombie_uiautomation(@device_id, "Google Location Accuracy NOT Enabled")
      end
      adb.shell('am force-stop com.android.settings')
    end

    def handle_toggle_sim_state(_sim_slot, state)
      log(:info, "Toggling SIM State to state: #{state}")
      success = false
      output = if state.to_s == '1'
                 start('com.browserstack.uiautomation.EnableSimStateTest', 30)
               else
                 start('com.browserstack.uiautomation.DisableSimStateTest', 30)
               end
      log(:info, "Output for handle_toggle_sim_state: #{output}")

      if output.include?("OK (1 test)")
        log(:info, "Changed SIM State to state: #{state} successfully")
        success = true
        push_to_zombie_uiautomation(@device_id, "Toggle SIM State #{state}")
      end

      unless success
        log(:error, "Failed to Toggle SIM State to state: #{state}")
        push_to_zombie_uiautomation(@device_id, "Failed to Toggle SIM State to #{state}")
      end
      adb.shell('am force-stop com.android.settings')
      success
    end

    def run_ui_automation(jarfile, class_name, timeout = 60)
      adb.shell("uiautomator runtest #{jarfile} -c #{class_name}", timeout: timeout)
    end

    def start(class_name, timeout = DEFAULT_TIMEOUT)
      increase_screen_off_timeout
      handle_popups

      command = "instrument -w -r -e debug false -e class '#{class_name}' #{RUNNER_CLASS_NAME}"
      log(:info, "Attempting UI Automation with command :: #{command}")

      output = adb.am(command, timeout: timeout)
      raise "NoTestsRan" if output =~ /(0 tests)/

      output
    end

    def vpn_popup_visible?
      current_focus.match?(/vpndialogs/)
    end

    def vpn_popup_visible_on_any_screen?
      current_foci.each do |focus|
        return true if focus.match?(/vpndialogs/)
      end
      false
    end

    def remove_esims(_os_version = nil)
      log(:info, "Removing esims from device")
      start('com.browserstack.uiautomation.RemoveEsimTest', 30)
      adb.shell('am force-stop com.android.settings')
    end

    def disable_mac_randomization(_os_version = nil)
      return unless @cleanup_function_runner.should_run?("disable_mac_randomization")

      automation_config_name = "disable_mac_randomization_rule"

      log(:info, "Running UI automation to disable mac randomization")
      begin
        toggle_via, intent = BrowserStack::UiAutomationRulesHelper.get_rule_for_automation(@device_id,
                                                                                           automation_config_name)
        if toggle_via == "intent_automation"
          log(:info, "Running Disable MAC Randomization via Intent")
          adb.shell('am force-stop com.android.settings')
          adb.shell("am start #{intent}")
        end
        output = start('com.browserstack.uiautomation.DisableMacRandomizationTest')
        if output.include?("OK (1 test)")
          log(:info, "Mac Randomization disabled successfully")
          push_to_zombie_uiautomation(@device_id, "Mac Randomization Disabled")
        else
          log(:info, "Mac Randomization not disabled successfully")
          push_to_zombie_uiautomation(@device_id, "Mac Randomization Not Disabled")
        end
      rescue StandardError => e
        log(:info, "Error while disabling MAC randomization: #{e.message} - #{e.backtrace}")
      end
    end

    def disable_popups_for_system_ui(_os_version = nil)
      device = AndroidDevice.new(@device_id, "Cleanup", @logger)
      return unless device.disable_popups_for_system_ui?

      automation_config_name = "disable_popups_for_system_ui"
      toggle_via, intent = BrowserStack::UiAutomationRulesHelper.get_rule_for_automation(@device_id,
                                                                                         automation_config_name)
      if toggle_via == "intent_automation"
        log(:info, "Running disable_popups_for_system_ui via Intent")
        adb.shell('am force-stop com.android.settings')
        adb.shell("am start #{intent}")
      end
      log(:info, "Disabling notification popups for System UI app (for slow charging popup)")
      output = start('com.browserstack.uiautomation.SystemUIDisablePopupTest')

      if output.include?("OK (1 test)")
        log(:info, "Disabled System UI Popup Successfully")
        push_to_zombie_uiautomation(@device, "Disabled System UI Popup Successfully")
      else
        log(:info, "Not Disabled System UI Popup Successfully")
        push_to_zombie_uiautomation(@device, "Not Disabled System UI Popup Successfully")
      end
    end

    def disable_popups_for_android_system(_os_version = nil)
      device = AndroidDevice.new(@device_id, "Cleanup", @logger)
      return unless device.disable_popups_for_android_system?

      log(:info, "Disabling notification popups for Android System app (for device management popup)")
      output = start('com.browserstack.uiautomation.AndroidSystemDisablePopupNotification')
      instrument_automation("disable_popups_for_android_system", output, @device_id)
    end

    def disable_popups_for_system_admin_channel(_os_version = nil)
      device = AndroidDevice.new(@device_id, "Cleanup", @logger)
      return unless device.disable_popups_for_system_admin_channel?

      log(:info, "Disabling notification popups for Android System IT admin channel (for device management popup)")
      output = start('com.browserstack.uiautomation.AndroidSystemDisablePopupAdminChannelNotification')
      instrument_automation("disable_popups_for_system_admin_channel", output, @device_id)
    end

    private

    def increase_screen_off_timeout
      adb.put_setting('system', 'screen_off_timeout', '86400000')
    end

    def current_focus
      adb.dumpsys('window').split("\n").grep(/mCurrentFocus/)[0]
    end

    def current_foci
      adb.dumpsys('window').split("\n").grep(/mCurrentFocus/)
    end

    def handle_popups
      # Dismissing popups before starting UI automation
      popup_helper.dismiss_crash_popup
      popup_helper.dismiss_deprecated_app_popup
      popup_helper.dismiss_esim_popup
    end

    def popup_helper
      # The JAR is not supported.
      # Using the test.runner automation to handle popups
      @popup_helper ||= BrowserStack::PopupHelper.new(device_id: @device_id, os_version: Gem::Version.new("11.0"))
    end

    def adb
      @adb ||= AndroidToolkit::ADB.new(udid: @device_id, path: BrowserStack::ADB)
    end

    def log(level, msg)
      if @logger.instance_of?(Logger)
        @logger.send(level.to_sym, msg)
      else
        @logger.send(level.to_sym, msg, @logger_params)
      end
    end

    def handle_click_anr_popup(os_version)
      log(:info, "Handling ANR popup regardless of configuration")
      if os_version.to_i >= 11
        log(:info, "Handling ANR popup using instrumentation test (Android 11+)")
        output = start('com.browserstack.uiautomation.ClickAnrPopupTest', 40)
      else
        log(:info, "Handling ANR popup using uiautomator jar (below Android 11)")
        output = run_ui_automation("/data/local/tmp/PopupHandlerAutomation.jar",
                                   "com.browserstack.popupHandler.ClickAnrPopup")
      end
      instrument_automation("Click_Anr_Popup_Test", output, @device_id)
    end

    def push_to_zombie_uiautomation(device_id, data = "", error = "", kind='android-cleanup-uiautomation')
      zombie_push('android', kind, error, '', data, device_id, '', '')
    rescue StandardError => e
      log(:error, "Push to zombie failed for uiautomation: #{e.message} #{e.backtrace.join("\n")}")
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  method = ARGV[0].to_s.strip.downcase.to_sym
  raise 'No method given' if method.nil?

  device_id = ARGV[1]
  os_version = ARGV[2]
  logger_params = {}
  logger_params[:device] = device_id
  logger_params[:component] = 'UiAutomationHelper_bash'

  logger = BrowserStack.init_logger("#{BrowserStack::LOGGING_DIR}/cleanup_#{device_id}.log", logger_params)
  helper = BrowserStack::UiAutomationHelper.new(device_id, logger)
  helper.send(method, os_version)
end
