# require 'pry'
require 'English'
require 'open3'
require_relative '../spec_helper'
require_relative '../../android/server'
require_relative '../../android/helpers/battery_helper'
require_relative '../../android/helpers/custom_media_manager'
require_relative '../../android/helpers/set_date_time_helper'
require_relative '../../android/helpers/performance_statistics'
require_relative '../../android/helpers/crash_logs_helper'
require_relative '../../android/lib/model_database'
require_relative '../../android/helpers/camera_media_injector'
require_relative '../../android/helpers/custom_certificate_helper'
require_relative '../../common/helpers'
require_relative '../../android/version_managers/input_injector_main_app_manager'
require_relative '../../android/lib/custom_exceptions'
require_relative '../../android/device_logger/device_logger'
require "/usr/local/.browserstack/mobile-common/utils/app_patching_util"
require 'android_toolkit'
include AppPatchingUtil # rubocop:todo Style/MixinUsage

# NOTE: See test_config.json for mock config values

describe 'all endpoints' do
  before do
    stub_const("CONFIG_FILE", "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  # These checks depend on mobile-common server utils code
  context 'when accepting params for a request' do
    it 'accepts requests when backticks are not included in the params' do
      get '/?some_param=123'
      expect(last_response.status).to eql(200)
    end

    it 'rejects requests when backticks are included in the params' do
      evil_param = CGI.escape('123`evilcodehere`')
      params = "some_param=#{evil_param}"
      get "/?#{params}"
      expect(last_response.status).to eql(400)
      expect(last_response.body).to eql('Illegal character(s) found in request params')
    end
  end

  context 'when trying to pass a command to create a file' do
    let(:file_name) { '/tmp/evil_file.txt' }
    after do
      File.delete(file_name) if File.exist?(file_name)
    end

    # Special case from vulnerability testing:
    it 'does not pass malicious values to /snapshot' do
      hack_command = CGI.escape("`touch #{file_name}`")
      params = "device=test_device&name=#{hack_command}"
      puts params
      get "/snapshot?#{params}"
      expect(last_response.status).to eql(400)
      expect(File.exist?(file_name)).to be false
    end
  end
end

describe "/stop" do
  let(:device) do
    "test_device"
  end

  let(:state_files_dir) do
    BrowserStack::STATE_FILES_DIR.to_s
  end

  let(:minimized_cleanup_reserved_file) do
    "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
  end

  let(:preserve_app_state_reserved_file) do
    "#{state_files_dir}/preserve_app_state_reserved_#{device}"
  end

  let(:device_obj) { double('device_obj') }
  let(:crypto_helper_obj) { double('crypto_helper_obj') }
  let(:crash_logs_helper) { double(CrashLogsHelper) }
  let(:device_logger_helper) { double('device_logger_helper') }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")

    ModelDatabaseMock = double('ModelDatabaseMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    AndroidToolkitMock = double('AndroidToolkitMock') # rubocop:todo Lint/ConstantDefinitionInBlock

    allow(ModelDatabaseMock).to receive(:property).and_return false
    allow(ModelDatabase).to receive(:new).and_return ModelDatabaseMock

    allow(AndroidToolkitMock).to receive(:devices_info).and_return "online"
    allow(AndroidToolkitMock).to receive(:battery_temperature).and_return "30"
    allow(AndroidToolkit::ADB).to receive(:new).and_return AndroidToolkitMock

    PerformanceStatisticsMock = double('PerformanceStatisticsMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    allow(PerformanceStatisticsMock).to receive(:start).and_return true
    allow(PerformanceStatistics).to receive(:new).and_return PerformanceStatisticsMock

    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(CrashLogsHelper).to receive(:new).and_return crash_logs_helper
    allow(crash_logs_helper).to receive(:process_crash_logs)
    allow(OSUtils).to receive(:execute).with(/systemctl/).at_most(2).times.and_return ""
    allow(OSUtils).to receive(:execute).with(/lsof/).and_return 384
    allow(AppInjection).to receive(:app_patching_enabled_in_app_automate?).and_return false

    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow_any_instance_of(Object).to receive(:pre_start).and_return true
    allow_any_instance_of(Object).to receive(:get_key_from_duplicate_session_summary)
    allow_any_instance_of(Object).to receive(:get_default_appium_version).and_return "1.17.0"
    allow_any_instance_of(Object).to receive(:get_stream_width).and_return 384
    allow_any_instance_of(EDS).to receive(:push_logs)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(device_obj)
    allow(device_obj).to receive(:uses_bstack_internet_app?).and_return(false)
    allow(device_obj).to receive(:model).and_return("Dummy")
    allow(CryptoMiningDetectionHelper).to receive(:new).and_return(crypto_helper_obj)
    allow(crypto_helper_obj).to receive(:disable_network_logging).and_return(true)
    allow(DeviceLogger).to receive(:new).and_return(device_logger_helper)
    allow(device_logger_helper).to receive(:init)
    allow(device_logger_helper).to receive(:start)
    allow(device_logger_helper).to receive(:update)
    allow(device_logger_helper).to receive(:stop)
    minimized_cleanup_reserved_file = "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
  end

  it 'should touch preserve_app_state_reserved_file for reserved sessions if preserve_app_state is passed true' do
    get "/stop?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&preserve_app_state=true"

    expect(last_response.status).to eql 200
    expect(File.exist?(preserve_app_state_reserved_file)).to eql true
    FileUtils.rm_f(preserve_app_state_reserved_file)
  end

  it 'should not touch preserve_app_state_reserved_file for reserved sessions if preserve_app_state is passed false' do
    get "/stop?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&preserve_app_state=false"

    expect(last_response.status).to eql 200
    expect(File.exist?(preserve_app_state_reserved_file)).to eql false
  end

  it 'should touch preserve_app_state_reserved_file for reserved sessions if preserve_app_state not passed' do
    get "/stop?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate"

    expect(last_response.status).to eql 200
    expect(File.exist?(preserve_app_state_reserved_file)).to eql false
  end

  it 'should touch preserve_app_state_reserved_file for non reserved sessions' do
    get "/stop?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate"

    expect(last_response.status).to eql 200
    expect(File.exist?(preserve_app_state_reserved_file)).to eql false
  end

  it 'should touch preserve_app_state_reserved_file for non app_automate' do
    get "/stop?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=automate"

    expect(last_response.status).to eql 200
    expect(File.exist?(preserve_app_state_reserved_file)).to eql false
  end
end

describe '/selenium_command' do
  let(:device) do
    "test_device"
  end
  let(:battery_instrumentor) { double('battery_instrumentor') }
  let(:device_logger_helper) { double('device_logger_helper') }

  let(:state_files_dir) do
    __dir__.to_s
  end

  let(:minimized_cleanup_reserved_file) do
    "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
  end

  let(:preserve_app_state_reserved_file) do
    "#{state_files_dir}/preserve_app_state_reserved_#{device}"
  end

  let(:root_command) { 'mock_root_command' }

  let(:browserstack_watcher_helper) { double(WatcherHelper) }
  let(:crash_logs_helper) { double(CrashLogsHelper) }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    stub_const("STATE_FILES_DIR", __dir__.to_s)

    allow(RootCommand).to receive(:new).and_return(root_command)
    allow(BatteryInstrumentor).to receive(:new).and_return(battery_instrumentor)
    allow(battery_instrumentor).to receive(:push).with('start_of_session')

    allow(DeviceLogger).to receive(:new).and_return(device_logger_helper)
    allow(device_logger_helper).to receive(:init)
    allow(device_logger_helper).to receive(:start)
    allow(device_logger_helper).to receive(:update)
    allow(device_logger_helper).to receive(:stop)

    ModelDatabaseMock = double('ModelDatabaseMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    allow(ModelDatabaseMock).to receive(:property).and_return false
    allow(ModelDatabase).to receive(:new).and_return ModelDatabaseMock

    PerformanceStatisticsMock = double('PerformanceStatisticsMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    allow(PerformanceStatisticsMock).to receive(:start).and_return true
    allow(PerformanceStatistics).to receive(:new).and_return PerformanceStatisticsMock

    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(OSUtils).to receive(:execute).with(/lsof/).and_return 384
    allow(AppInjection).to receive(:app_patching_enabled_in_app_automate?).and_return false
    allow(WatcherHelper).to receive(:new).and_return browserstack_watcher_helper
    allow(browserstack_watcher_helper).to receive(:stop)
    allow(CrashLogsHelper).to receive(:new).and_return crash_logs_helper
    allow(crash_logs_helper).to receive(:clear_crash_logs_buffer)
    allow(crash_logs_helper).to receive(:start_crash_logs_capture)

    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow_any_instance_of(Object).to receive(:pre_start).and_return true
    allow_any_instance_of(Object).to receive(:get_default_appium_version).and_return "1.17.0"
    allow_any_instance_of(Object).to receive(:get_stream_width).and_return 384
    allow_any_instance_of(Object).to receive(:playwright_command).and_return [true, true, "URL"]
    allow_any_instance_of(EDS).to receive(:push_logs)

    allow_any_instance_of(BrowserStack::AndroidDevice)
      .to receive(:load_config)
      .and_return(
        # Must stub non-empty config otherwise AndroidDevice will raise exception
        JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']['test_device']
      )

    allow_any_instance_of(BrowserStack::AndroidDevice).to receive(:pm_clear_command_fails?).and_return(true)

    minimized_cleanup_reserved_file = "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
  end

  it 'should only touch minimized_cleanup_reserved_file for reserved sessions' do
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate",
        { "app_automate_custom_params" => '' }

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql true
    expect(File.exist?(preserve_app_state_reserved_file)).to eql false
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end

  it 'should call download_and_install_app for 1+ reserved sessions' do
    FileUtils.touch(minimized_cleanup_reserved_file)

    customMediaManagerMock = double('customMediaManagerMock')
    expect(customMediaManagerMock).to_not receive(:download_and_push)
    expect(customMediaManagerMock).to_not receive(:verify_insertion_to_mediastore)

    expect(CustomMediaManager).to receive(:new).and_return customMediaManagerMock
    expect_any_instance_of(Object).to receive(:download_and_install_app)

    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&s3_app"\
        "_url=abc&custom_media=1", { "app_automate_custom_params" => '' }

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql true
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end

  it 'should not ignore "custom_media", "app_store_username", "app_store_password", :language, :localization, '\
     '"timezone", "latitude", "longitude", :networkSimulation for 1 reserved sessions' do
    customMediaManagerMock = double('customMediaManagerMock')
    expect(customMediaManagerMock).to receive(:download_and_push).and_return true
    expect(customMediaManagerMock).to receive(:verify_insertion_to_mediastore).and_return true

    batteryHelperMock = double('BatteryHelperMock')
    expect(batteryHelperMock).to receive(:mock_property).and_return true

    expect(CustomMediaManager).to receive(:new).and_return customMediaManagerMock
    expect(BatteryHelper).to receive(:new).and_return batteryHelperMock

    expect_any_instance_of(Object).to receive(:disable_device_animations).and_return true
    expect_any_instance_of(Object).to receive(:perform_google_login).and_return true
    expect_any_instance_of(Object).to receive(:set_device_locale).and_return true
    expect_any_instance_of(Object).to receive(:get_timezone).and_return true
    expect_any_instance_of(Object).to receive(:set_gpslocation).and_return true
    expect_any_instance_of(Object).to receive(:setup_network_simulation).and_return true

    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&custom_"\
        "media=1&disableAnimations=true&language=en&timezone=fr&latitude=123&longitude=123&networkSimulation=true",
        { "app_automate_custom_params" => '' }

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql true
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end

  it 'should ignore "custom_media", "app_store_username", "app_store_password", :language, :localization, "timezone", '\
     '"latitude", "longitude", :networkSimulation for 1+ reserved sessions' do
    FileUtils.touch(minimized_cleanup_reserved_file)

    customMediaManagerMock = double('customMediaManagerMock')
    expect(customMediaManagerMock).to_not receive(:download_and_push)
    expect(customMediaManagerMock).to_not receive(:verify_insertion_to_mediastore)

    expect(CustomMediaManager).to receive(:new).and_return customMediaManagerMock

    expect_any_instance_of(Object).to_not receive(:disable_device_animations)
    expect_any_instance_of(Object).to_not receive(:verify_google_login)
    expect_any_instance_of(Object).to_not receive(:set_device_locale)
    expect_any_instance_of(Object).to_not receive(:get_timezone)
    expect_any_instance_of(Object).to_not receive(:get_mock_location_app)
    expect_any_instance_of(Object).to_not receive(:setup_network_simulation)

    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&custom_"\
        "media=1&disableAnimations=true&language=en&timezone=fr&latitude=123&longitude=123&networkSimulation=true",
        { "app_automate_custom_params" => '' }

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql true
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end

  it 'should catch any unhandled exceptions raised in execute_fire_cmd' do
    FileUtils.touch(minimized_cleanup_reserved_file)

    customMediaManagerMock = double('customMediaManagerMock')
    expect(customMediaManagerMock).to_not receive(:download_and_push)
    expect(customMediaManagerMock).to_not receive(:verify_insertion_to_mediastore)

    allow(CustomMediaManager).to receive(:new).and_raise("Something went wrong")

    expect_any_instance_of(Object).to receive(:ensure_thread_killed).once
    expect_any_instance_of(Object).to_not receive(:disable_device_animations)
    expect_any_instance_of(Object).to_not receive(:verify_google_login)
    expect_any_instance_of(Object).to_not receive(:set_device_locale)
    expect_any_instance_of(Object).to_not receive(:get_timezone)
    expect_any_instance_of(Object).to_not receive(:get_mock_location_app)
    expect_any_instance_of(Object).to_not receive(:setup_network_simulation)

    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&reserveDevice=true&genre=app_automate&custom_"\
        "media=1&disableAnimations=true&language=en&timezone=fr&latitude=123&longitude=123&networkSimulation=true",
        { "app_automate_custom_params" => '' }

    response = JSON.parse(last_response.body)
    expect(last_response.status).to eql 500
    expect(response["kind"]).to eql "unhandled_exception"
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql true
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end

  it 'should not touch minimized_cleanup_reserved_file for non reserved sessions' do
    FileUtils.rm_f(minimized_cleanup_reserved_file)
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&genre=app_automate",
        { "app_automate_custom_params" => '' }

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql false
  end

  it 'should trigger playwright flow when is_playwright is true' do
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&is_playwright=true"

    expect(last_response.status).to eql 200
    expect(File.exist?(minimized_cleanup_reserved_file)).to eql false
  end

  it 'should skip tunnel setup if allow_device_mock_server is passed as true' do
    expect_any_instance_of(Object).not_to receive(:tunnel_setup)
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&genre=app_automate&"\
        "allow_device_mock_server=true&hosts=0.0.0.0&app_testing_bundle_id=XYZ", { "app_automate_custom_params" => '' }
    expect(last_response.status).to eql 200
  end

  it 'it should run tunnel setup if allow_device_mock_server is passed as false' do
    expect_any_instance_of(Object).to receive(:tunnel_setup)
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&genre=app_automate&"\
        "allow_device_mock_server=false&hosts=0.0.0.0&app_testing_bundle_id=XYZ",
        { "app_automate_custom_params" => '' }
    expect(last_response.status).to eql 200
  end

  it "should enable transparent mode if option is passed" do
    expect(TransparentNetworkMode).to receive(:enable).and_return(true)
    get "/selenium_command?device=#{device}&deviceName=#{device}-9.0&genre=app_automate&"\
          "enableTransparentMode=true"
    expect(last_response.status).to eql 200
  end

  after(:each) do
    FileUtils.rm_f(minimized_cleanup_reserved_file)
  end
end

context 'when preInstallApp parameter is true' do
  let(:params) do
    {
      'device' => 'device1',
      'app_package' => 'com.example.test',
      'app_automate_custom_params' => { 'preInstallApp' => true }.to_json,
      'genre' => 'app_automate',
      "user_id" => "test_user"
    }
  end

  before do
    allow(self).to receive(:check_and_enable_mobile_data?).and_return(false)
    allow_any_instance_of(WriteSessionInfo).to receive(:save).and_return(nil)
    allow(MCSPT).to receive(:stop_session_running_on_device_async).and_return(nil)
    allow_any_instance_of(CryptoMiningDetectionHelper).to receive(:enable_network_logging).and_return(nil)
    allow_any_instance_of(BatteryInstrumentor).to receive(:push).and_return(nil)
    allow(AppInjection).to receive(:app_patching_enabled_in_app_live?).and_return(false)
    allow(AppInjection).to receive(:add_patch_log_file).and_return(nil)
    allow(self).to receive(:calculate_privoxy_port).and_return(8080)
    allow(self).to receive(:tunnel_setup).and_return("none")
    allow(self).to receive(:calc_usb_tunnel_ip).and_return("************")
    allow(self).to receive(:set_device_locale).and_return(nil)
    allow(self).to receive(:check_and_log_usb_internet).and_return(nil)
    allow(self).to receive(:start_network_usage_tracker).and_return(nil)
    allow(self).to receive(:monitor_device_logger_metric).and_return(nil)
    allow(self).to receive(:write_rtc_params).and_return({ interaction_args: '', stream_width: '' })
    allow(AppInjection).to receive(:touch_app_injection_state_file).and_return(nil)
  end

  it 'should not attempt to download and install the app' do
    expect(self).not_to receive(:download_and_install_app)
    post '/app_start', params.to_json
  end
end

describe '/snapshot_hub' do
  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
  end

  context 'valid scenarios for snapshot request' do
    it 'returns a 200 with an done response' do
      allow(Snapshotter).to receive(:valid_request?).and_return(true)
      allow(Snapshotter).to receive(:valid_session?).and_return(true)
      allow(Snapshotter).to receive(:can_take_screenshots?).and_return(true)
      allow(Snapshotter).to receive(:take_screenshot).and_return('')
      get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder'
      expect(last_response.status).to eql(200)
      expect(last_response.body).to eql('done')
    end

    context '#useADBforScreenshot param' do
      it "passes the flag as 'true' in screenshot cmd if the param's value is true" do
        expect(Snapshotter).to receive(:valid_request?).and_return(true)
        expect(Snapshotter).to receive(:valid_session?).and_return(true)
        expect(Snapshotter).to receive(:can_take_screenshots?).and_return(true)
        expect_any_instance_of(Object).to receive(:script_logger_args).and_return("script_logger")

        screenshot_cmd = "ruby #{DEBUG_SCREENSHOT_SCRIPT} test_device 1234 test_bucket##test_folder##test_file "\
                         "portrait abcd xyz false true false 2>&1 | while read line; do echo script_logger \"$line\" "\
                         ">> /var/log/browserstack/hub_snapshot_test_device.log; done &"
        expect(Snapshotter).to receive(:take_screenshot).with(screenshot_cmd).and_return('')

        get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder&orientation='\
            'portrait&key=abcd&secret=xyz&useADBforScreenshot=true'

        expect(last_response.status).to eql(200)
        expect(last_response.body).to eql('done')
      end

      it "passes the flag as 'false' in screenshot cmd if the param's value is false" do
        expect(Snapshotter).to receive(:valid_request?).and_return(true)
        expect(Snapshotter).to receive(:valid_session?).and_return(true)
        expect(Snapshotter).to receive(:can_take_screenshots?).and_return(true)
        expect_any_instance_of(Object).to receive(:script_logger_args).and_return("script_logger")

        screenshot_cmd = "ruby #{DEBUG_SCREENSHOT_SCRIPT} test_device 1234 test_bucket##test_folder##"\
                         "test_file portrait abcd xyz false false false 2>&1 | while read line; do echo script_logger"\
                         " \"$line\" >> /var/log/browserstack/hub_snapshot_test_device.log; done &"
        expect(Snapshotter).to receive(:take_screenshot).with(screenshot_cmd).and_return('')

        get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder&orientation='\
            'portrait&key=abcd&secret=xyz&useADBforScreenshot=false'

        expect(last_response.status).to eql(200)
        expect(last_response.body).to eql('done')
      end

      it "passes the flag as 'false' in screenshot cmd if the param is not passed" do
        expect(Snapshotter).to receive(:valid_request?).and_return(true)
        expect(Snapshotter).to receive(:valid_session?).and_return(true)
        expect(Snapshotter).to receive(:can_take_screenshots?).and_return(true)
        expect_any_instance_of(Object).to receive(:script_logger_args).and_return("script_logger")

        screenshot_cmd = "ruby #{DEBUG_SCREENSHOT_SCRIPT} test_device 1234 test_bucket##test_folder##test_file "\
                         "portrait abcd xyz false false false 2>&1 | while read line; do echo script_logger \"$line\" "\
                         ">> /var/log/browserstack/hub_snapshot_test_device.log; done &"
        expect(Snapshotter).to receive(:take_screenshot).with(screenshot_cmd).and_return('')

        get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder&orientation='\
            'portrait&key=abcd&secret=xyz'

        expect(last_response.status).to eql(200)
        expect(last_response.body).to eql('done')
      end
    end
  end

  context 'invalid scenarios for snapshot request' do
    it 'returns 404 if device is not passed in request' do
      get '/snapshot_hub?file=test_file&bucket=test_bucket&folder=test_folder'
      expect(last_response.status).to eql(404)
      expect(last_response.body).to eql('device not found')
    end

    it 'returns 404 if folder is not passed in request' do
      get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket'
      expect(last_response.status).to eql(404)
      expect(last_response.body).to eql('')
    end

    it 'returns 404 if request is passed for previous session' do
      allow(MobileSessionInfo).to receive(:file_path).and_return('/path/to/random/file')
      allow(Snapshotter).to receive(:valid_request?).and_return(true)
      allow(Snapshotter).to receive(:valid_session?).and_return(false)
      get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder'
      expect(last_response.status).to eql(404)
      expect(last_response.body).to eql('')
    end

    it 'returns 200 if too many snapshot processes are already running' do
      allow(MobileSessionInfo).to receive(:file_path).and_return('/path/to/random/file')
      allow(Snapshotter).to receive(:valid_request?).and_return(true)
      allow(Snapshotter).to receive(:valid_session?).and_return(true)
      allow(Snapshotter).to receive(:can_take_screenshots?).and_return(false)
      expect(Snapshotter).not_to receive(:take_screenshot)
      get '/snapshot_hub?device=test_device&file=test_file&bucket=test_bucket&folder=test_folder'
      expect(last_response.status).to eql(200)
      expect(last_response.body).to eql('done')
    end
  end
end

describe "#force_stop_bundle_id" do
  let(:device_id) { "test_device_id" }
  let(:bundle_id) { "com.example.app" }
  let(:adb) { instance_double(AndroidToolkit::ADB) }

  before do
    allow(AndroidToolkit::ADB).to receive(:new).with(udid: device_id, path: BrowserStack::ADB).and_return(adb)
  end

  it "should execute force-stop command successfully" do
    expect(adb).to receive(:shell).with("am force-stop #{bundle_id}").and_return(nil)
    expect(force_stop_bundle_id(device_id, bundle_id)).to eq(true)
  end

  it "should handle ADBError and log an error" do
    adb_error = AndroidToolkit::ADB::ADBError.new("ADB command failed")
    expect(adb).to receive(:shell).with("am force-stop #{bundle_id}").and_raise(adb_error)
    expect(BrowserStack.logger).to receive(:error).with(/ADBError.*#{bundle_id}.*ADB command failed/)
    expect(force_stop_bundle_id(device_id, bundle_id)).to eq(false)
  end

  it "should handle generic errors and log an error" do
    generic_error = StandardError.new("Some unexpected error")
    expect(adb).to receive(:shell).with("am force-stop #{bundle_id}").and_raise(generic_error)
    expect(BrowserStack.logger).to receive(:error).with(/Error.*#{bundle_id}.*Some unexpected error/)
    expect(force_stop_bundle_id(device_id, bundle_id)).to eq(false)
  end
end

describe "/timeout_session" do
  let(:exitstatus) { double('exitstatus') }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow_any_instance_of(Object).to receive(:clean_in_session).and_return(true)
    allow_any_instance_of(Object).to receive(:system).and_return(true)
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow(Open3).to receive(:pipeline).and_return([exitstatus, "op2"])
  end

  it 'should return 200 when bash returns 0 exitstatus' do
    allow(exitstatus).to receive(:exitstatus).and_return(0)
    get '/timeout_session?build_id=build_id&automate_session_id=session_id&device=test_device&terminal_ip=*******'\
        '&device_name=test_device'

    expect(last_response.status).to eq(200)
  end

  it 'should return 500 when bash returns 1 exitstatus' do
    allow(exitstatus).to receive(:exitstatus).and_return(1)

    get '/timeout_session?build_id=build_id&automate_session_id=session_id&device=test_device&terminal_ip=*******'\
        '&device_name=test_device'

    expect(last_response.status).to eq(500)
  end
end

describe 'set_gpslocation' do
  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)

    allow(self).to receive(:system)
    allow_any_instance_of(Object).to receive(:zombie_push).and_return('')
    allow_any_instance_of(Object).to receive(:update_feature_usage).and_return('')
  end

  let(:latitude) { '-xx.xx' }
  let(:longitude) { '-yy.yy' }
  let(:session_id) { 'random_session_id' }
  let(:genre) { 'some_genre' }

  context 'does correct location mocking' do
    before do
      allow(Thread).to receive(:bs_run)
    end

    context 'genre does not require waiting' do
      let(:genre) { 'live_testing' }

      it 'calls spawn_process' do
        expect_any_instance_of(Object).to receive(:spawn_process)

        set_gpslocation(latitude, longitude, genre, session_id)
      end
    end

    context 'genre requires waiting' do
      it 'calls system' do
        expect(self).to receive(:system)

        set_gpslocation(latitude, longitude, genre, session_id)
      end
    end
  end

  context 'confirmation thread' do
    it 'starts confirm_device_location in a new thread' do
      allow_any_instance_of(Object).to receive(:spawn_process).and_return('')

      expect(Thread).to receive(:bs_run).and_yield
      expect_any_instance_of(Object)
        .to receive(:confirm_device_location)
        .with(latitude, longitude, session_id, genre, {})
        .and_return('')

      set_gpslocation(latitude, longitude, genre, session_id)
    end
  end
end

describe '#stop_streaming_and_interaction' do
  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
    @device = 'test_device'
  end

  it 'calls stop webrtc method for automate in live-script' do
    params = {
      debugger_port: 9876
    }
    expect_any_instance_of(Object)
      .to receive(:write_rtc_params)
      .with(params.merge({ debugger_url: 'https://*************/json?debug_port=9876' }))
      .and_return({ stream_width: 384, interaction_args: '' })

    expect_any_instance_of(Object).to receive(:script_logger_args).and_return('script_logger')

    expected_live_script_cmd = "bash #{LIVE_SCRIPT} stop_webrtc_streaming_automate test_device '' '' '384' 2>&1 "\
                               "| while read line; do echo script_logger \"$line\"; done &"
    expect(self).to receive(:system).with(expected_live_script_cmd)

    expect(FileUtils).to receive(:rm_f).with("#{STATE_FILES_DIR}/interactive_session_file_#{@device}")

    stop_streaming_and_interaction(params)
  end
end

describe '#start_streaming_and_interaction' do
  let(:input_injector) { double('mock_input_injector') }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(InputInjectorMainAppManager).to receive(:new).and_return(input_injector)
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
    @device = 'test_device'
  end

  it 'calls webrtc method for automate in live-script and initiates input-injector' do
    params = {
      debugger_port: 9876
    }
    expect_any_instance_of(Object)
      .to receive(:write_rtc_params)
      .with(params.merge({ debugger_url: 'https://*************/json?debug_port=9876' }))
      .and_return({ stream_width: 384, interaction_args: '' })

    expect(File).to receive(:exist?).with("#{STATE_FILES_DIR}/session_test_device").and_return(true)
    expect(File).to receive(:read).with("#{STATE_FILES_DIR}/session_test_device").and_return("{\"test\": \"value\"}")
    expect(File).to receive(:read).and_return(params.to_json.to_s)
    expect_any_instance_of(Object).to receive(:script_logger_args).and_return('script_logger')

    expected_live_script_cmd = "bash #{LIVE_SCRIPT} "\
    "start_webrtc_streaming_automate test_device '' '' "\
    "'384' '' '' '' #{params[:debugger_port]} 2>&1 | while read line; do echo script_logger \"$line\"; done &"
    expect(self).to receive(:system).with(expected_live_script_cmd)

    # input injector assertions
    input_injector_apk_path = '/usr/local/.browserstack/deps/input_injector/v3/InputInjector.apk'
    allow(input_injector).to receive(:apk_path).and_return(input_injector_apk_path)
    input_injector_push = "adb -s test_device push #{input_injector_apk_path} /data/local/tmp/InputInjector.apk"
    input_injector_start = "adb -s test_device shell 'CLASSPATH=\"/data/local/tmp/InputInjector.apk\" app_process "\
                           "/ com.browserstack.inputinjector.Main &' &"
    expect(self).to receive(:system).with(input_injector_push)
    expect(self).to receive(:system).with(input_injector_start)

    expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/interactive_session_file_#{@device}")

    start_streaming_and_interaction(params)
  end
end

describe 'handle_automate_media_projection_popup' do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
    @device = 'test_device'
  end

  xit 'handle media projection popup if detected' do
    perm_window = "adb -s #{@device} shell dumpsys window "\
    " | grep \"mCurrentFocus.*MediaProjectionPermissionActivity.*\""
    cmd = "adb -s #{@device} shell \"am instrument -w -r -e debug false -e class "\
    "'com.browserstack.uiautomation.PopUpHandlerTest' "\
    "com.browserstack.uiautomation.test/androidx.test.runner.AndroidJUnitRunner\""
    allow(self).to receive(:system).with(perm_window).and_return(true, false)
    expect(self).to receive(:system).with(cmd)
    expect(self).not_to receive(:system).with(cmd)
    handle_automate_media_projection_popup
  end
end

describe 'confirm_device_location' do
  let(:latitude) { '-xx.xx' }
  let(:longitude) { '-yy.yy' }
  let(:session_id) { 'random_session_id' }
  let(:genre) { 'live' }
  let(:timestamp) { 10_000 }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:sleep)
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  context 'correct location checking' do
    it 'checks for at most 6 times' do
      counter = 0
      allow_any_instance_of(Object)
        .to receive(:compare_device_location)
          .with(latitude, longitude) { (counter += 1) && false }

      confirm_device_location(latitude, longitude, genre, session_id)
      expect(counter).to eql(6)
    end
  end

  context 'zombie calls' do
    let(:zombie_kind_initiated) { 'location_change_initiated_for' }
    let(:zombie_kind_success) { 'location_change_confirmed_for' }
    let(:common_zombie_params) { ['', '', timestamp.to_s, '', session_id, { is_app_accessibility: nil }] }

    before do
      allow(BrowserStack.logger).to receive(:info)
      allow(Time).to receive(:now).and_return(timestamp)
    end

    it 'informs zombie at starting' do
      allow_any_instance_of(Object).to receive(:compare_device_location).and_return(false)
      allow_any_instance_of(Object)
        .to receive(:zombie_push)
        .with('android', "#{zombie_kind_initiated}_#{genre}", *common_zombie_params)

      confirm_device_location(latitude, longitude, session_id, genre)
    end

    it 'informs zombie at success' do
      allow_any_instance_of(Object)
        .to receive(:zombie_push)
          .with('android', "#{zombie_kind_initiated}_#{genre}", *common_zombie_params) {
              allow_any_instance_of(Object)
                .to receive(:zombie_push)
                .with('android', "#{zombie_kind_success}_#{genre}", *common_zombie_params)
            }
      allow_any_instance_of(Object).to receive(:compare_device_location).and_return(true)

      confirm_device_location(latitude, longitude, session_id, genre)
    end
  end
end

describe 'parsing test_params' do
  context 'parsing sharding espresso params' do
    it 'should set numshards, shardindex and newRunListenerMode in the espresso test params' do
      test_params_sent = '{
        "numShards": "4",
        "shardIndex": "2",
        "newRunListenerMode": true
      }'
      test_params_expected = " -e numShards 4 -e shardIndex 2 -e newRunListenerMode true"
      expect(get_test_params(test_params_sent)).to eq(test_params_expected)
    end

    it 'should set array value for numshards, shardindex and newRunListenerMode in the espresso test params' do
      test_params_sent = '{
        "numShards": ["4"],
        "shardIndex": ["2"],
        "newRunListenerMode": ["true"]
      }'
      test_params_expected = " -e numShards 4 -e shardIndex 2 -e newRunListenerMode true"
      expect(get_test_params(test_params_sent)).to eq(test_params_expected)
    end

    it 'should set class in the espresso test params' do
      test_params_sent = '{
        "class":["com.package.class1", "com.package.class2"]
      }'
      test_params_expected = " -e class com.package.class1,com.package.class2"
      expect(get_test_params(test_params_sent)).to eq(test_params_expected)
    end

    it 'should set package names in the espresso test params' do
      test_params_sent = '{
        "package":["com.package1", "com.package2"]
      }'
      test_params_expected = " -e package com.package1,com.package2"
      expect(get_test_params(test_params_sent)).to eq(test_params_expected)
    end

    it 'should set annotations and size in the epsresso test params' do
      test_params_sent = '{
        "annonation":["p1", "p2"],
        "size":["s1", "s2"]
      }'
      test_params_expected = " -e annonation p1,p2 -e size s1,s2"
      expect(get_test_params(test_params_sent)).to eq(test_params_expected)
    end
  end
end

describe "component_breakdown" do
  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  it "should add to component_breakdown_hash" do
    component_breakdown_hash = {}
    component_breakdown("abc", Time.now, component_breakdown_hash)
    expect(component_breakdown_hash).to have_key("abc")
  end
end

describe 'compare_device_location' do
  let(:special_latitude) { '0.0010000' }
  let(:special_longitude) { '0.0010000' }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  it 'gets coordinates from device' do
    received = false
    allow_any_instance_of(Object).to receive(:get_geolocation).with(no_args) do
      received = true
      '110,111'
    end
    compare_device_location('110', '110')
    expect(received).to be(true)
  end

  context 'compares given coordinates with device coordinates' do
    it 'returns true on equal' do
      allow_any_instance_of(Object).to receive(:get_geolocation).and_return('10,20')
      latitude = '10'
      longitude = '20'
      expect(compare_device_location(latitude, longitude)).to be(true)
    end

    it 'return false on unequal' do
      allow_any_instance_of(Object).to receive(:get_geolocation).and_return('10.1,20')
      latitude = '10'
      longitude = '20'
      expect(compare_device_location(latitude, longitude)).to be(false)
    end

    it 'trims results up to 4 digits after decimal equal' do
      allow_any_instance_of(Object).to receive(:get_geolocation).and_return('90.00001,90.00001')
      latitude = '90.00000'
      longitude = '90.00000'
      expect(compare_device_location(latitude, longitude)).to be(true)
    end

    it 'trims results up to 4 digits after decimal unequal' do
      allow_any_instance_of(Object).to receive(:get_geolocation).and_return('90.0009,90.0009')
      latitude = '90.00099'
      longitude = '90.00099'
      expect(compare_device_location(latitude, longitude)).to be(false)
    end

    it 'handles negatives' do
      allow_any_instance_of(Object).to receive(:get_geolocation).and_return('-100,-20')
      latitude = '-100.00001'
      longitude = '-20.00001'
      expect(compare_device_location(latitude, longitude)).to be(true)
    end

    it 'handles special case' do
      allow_any_instance_of(Object)
        .to receive(:get_geolocation)
        .and_return("#{special_latitude},#{special_longitude}")
      latitude = '0'
      longitude = '0'
      expect(compare_device_location(latitude, longitude)).to be(true)
    end

    it 'handles special case with negatives' do
      allow_any_instance_of(Object)
        .to receive(:get_geolocation)
        .and_return("#{special_latitude},#{special_longitude}")
      latitude = '-0.0'
      longitude = '-0.0'
      expect(compare_device_location(latitude, longitude)).to be(true)
    end
  end
end

describe 'disable_animations' do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  context "error scenarios" do
    it 'returns false if device is not set' do
      expect(disable_device_animations(nil)).to be(false)
    end

    it 'returns false if device is random' do
      expect(disable_device_animations("random_device")).to be(false)
    end
  end

  context "success scenarios" do
    let(:device) do
      "some_device_id"
    end

    it 'should run the adb commands to disable the animations' do
      allow_any_instance_of(Object)
        .to receive(:disable_device_animations)
        .with("some_device_id")
        .and_return(true)

      expect(disable_device_animations(device)).to be(true)
    end
  end
end

describe 'set_hidden_policy' do
  let(:device) { "mock-device-id" }
  let(:params) { {} }
  let(:adb) { double('adb') }

  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
  end

  context "device_version >= 10 " do
    let(:device_version) do
      "11"
    end

    it 'set hidden API policies' do
      expect(adb).to receive(:put_setting).with("global", "hidden_api_policy", 1).and_return(true)
      set_hidden_policy(device_version)
    end
  end

  context "device_version = 9 " do
    let(:device_version) do
      "9"
    end

    it 'set hidden API policies' do
      expect(adb).to receive(:put_setting).with("global", "hidden_api_policy_pre_p_apps", 1).and_return(true)
      expect(adb).to receive(:put_setting).with("global", "hidden_api_policy_p_apps", 1).and_return(true)
      set_hidden_policy(device_version)
    end

    it 'should not raise error if setting failed' do
      allow(adb).to receive(:put_setting).with("global", "hidden_api_policy_pre_p_apps", 1)
                                         .and_raise(AndroidToolkit::ADB::ExecutionError, "boom")
      expect(@mock_logger).to receive(:error)
      set_hidden_policy(device_version)
    end
  end
end

describe 'open_app' do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  context "prod" do
    before do
      allow_any_instance_of(StaticConf::Config).to receive("[]").with("rails_endpoint")
                                                                .and_return("http://www.browserstack.com")
    end

    it 'should return 404 if prod' do
      get '/open_app', { device: 'test_device', package_name: 'com.android.chrome' }
      expect(last_response.status).to eq(404)
      expect(last_response.body)
        .to eq({ statusCode: 1, statusMessage: "Not available on production", data: nil }.to_json)
    end
  end

  context "not prod" do
    before do
      allow_any_instance_of(StaticConf::Config).to receive("[]").with("rails_endpoint")
                                                                .and_return("http://asdf.ngrok.io")
    end

    it 'should return 200 if not prod' do
      get '/open_app', { device: 'test_device', package_name: 'com.android.chrome' }
      expect(last_response.status).to eq(200)
      expect(last_response.body)
        .to eq({ statusCode: 0, statusMessage: "com.android.chrome has been launched", data: nil }.to_json)
    end

    it 'should return 422 for apps not allowed' do
      get '/open_app', { device: 'test_device', package_name: 'org.mozilla.firefox' }
      expect(last_response.status).to eq(422)
      expect(last_response.body)
        .to eq({ statusCode: 1, statusMessage: "org.mozilla.firefox can't be opened", data: nil }.to_json)
    end

    it 'should call system with correct command' do
      expect_any_instance_of(Object).to receive(:system)
        .with('adb -s test_device shell am start -n $(adb -s test_device shell cmd package resolve-activity --brief '\
              '-c android.intent.category.LAUNCHER com.android.chrome | tail -1 )').and_return(true)
      get '/open_app', { device: 'test_device', package_name: 'com.android.chrome' }
      expect(last_response.status).to eq(200)
      expect(last_response.body)
        .to eq({ statusCode: 0, statusMessage: "com.android.chrome has been launched", data: nil }.to_json)
    end
  end
end

describe 'update_device_log_level' do
  let(:session_id) { "session_id" }
  let(:device_id) { "pixel_3_device_id" }

  before do
    stub_const("CONFIG_FILE", "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow(File).to receive(:exist?).and_return(true)
    allow(@mock_logger).to receive(:info)
  end

  it 'should return 400 if log_type is missing' do
    session_file = {}
    allow(File).to receive("read").and_return(session_file.to_json)
    get '/update_device_log_level', { device: device_id }
    expect(last_response.status).to eq(400)
    expect(last_response.body).to eq({ error: "Invalid log_level: " }.to_json)
  end

  it 'should return 200 if log_type is present' do
    session_file = { 'installed_other_apps_details' => { "hashed_id" => {
      app_path: "app_path", bundle_id: "bundle_id"
    } } }
    allow(File).to receive("read").and_return(session_file.to_json)
    get '/update_device_log_level', { device: device_id, log_level: "2" }
    expect(last_response.status).to eq(200)
  end
end

describe 'tunnel setup' do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  context 'init_proxies' do
    it 'stops mitm_proxy if should_start_mitm is true with privoxy, assign correct port' do
      allow_any_instance_of(Object).to receive(:stop_privoxy).with(no_args).and_return(true)
      allow_any_instance_of(Object)
        .to receive(:stop_mitm_proxy)
        .with(anything, anything)
        .and_return(true)
      params = {}
      init_proxies(params, { 'device' => { 'network_logs_port' => 111 } }, true, 'device')
      expect(params['network_logs_port']).to be(111)
    end

    it 'should disable mitm proxy if should_start_mitm is false' do
      received = false
      allow_any_instance_of(Object)
        .to receive(:disable_mitm_proxy)
        .with(anything, anything, anything) do
          received = true
        end
      init_proxies({}, {}, false, 'device')
      expect(received).to be(true)
    end
  end
end

describe 'pre_start' do
  let(:device) { 'test_device' }
  let(:adb) { double('abd') }
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:fatal)
    stub_const("STATE_FILES_DIR", __dir__)
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
    allow(adb).to receive(:shell).and_return(true)
    allow_any_instance_of(Object).to receive(:handle_dedicated_device_file).and_return(true)
  end

  context 'when a cleanupdone file is present for the specified device' do
    before do
      File.write(cleanup_file_for_device(device), "cleanupdone file for rspec test")
    end

    it 'returns false' do
      expect(pre_start(device, { 'loader_trigger' => 'switcher' })).to eql(false)
    end
  end

  context 'when neither cleanup or session file is present for the specified device' do
    it 'returns true' do
      expect(pre_start(device, { 'loader_trigger' => 'switcher' })).to eql(true)
    end
  end

  context 'when a session file with a different user is present for the specified device' do
    before do
      File.write(cleanup_file_for_device(device), "{ \"user_id\": \"notme\"}")
    end

    it 'returns false' do
      allow(Object).to receive(:zombie_push).and_return(nil)
      expect(pre_start(device, { 'loader_trigger' => 'switcher', 'user_id' => 'me' })).to eql(false)
    end
  end

  after(:each) do
    FileUtils.rm_f(cleanup_file_for_device(device))
    FileUtils.rm_f("#{STATE_FILES_DIR}/session_#{device}")
  end
end

describe 'CA Certificate Installation' do
  let(:device) { 'test_device' }
  let(:session_id) { 'test_session_id' }
  let(:custom_certificate_helper) { double('custom_certificate_helper') }

  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow_any_instance_of(Object).to receive(:pre_start).and_return(true)
    allow_any_instance_of(Object).to receive(:update_feature_usage).and_return(nil)
    allow_any_instance_of(EDS).to receive(:push_logs)
    allow_any_instance_of(Object).to receive(:mark_event_start).and_return(nil)
    allow_any_instance_of(Object).to receive(:mark_event_end).and_return(nil)

    # Mock the device filter middleware
    allow_any_instance_of(Object).to receive(:add_device_features_to_params).and_return({})
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return(false)
  end

  context 'when installing CA certificate in execute_fire_cmd' do
    let(:params) do
      {
        'device' => device,
        'automate_session_id' => session_id,
        'event_hash' => {},
        'is_app_testing' => true
      }
    end

    it 'should call install_ca_cert when filetype is certificates_cer_android' do
      params['customCertificateFile'] = '{"s3_url":"https://example.com/cert.cer","filename":"cert.cer","filetype":"certificates_cer_android","password":""}'

      expect(CustomCertificate).to receive(:new)
        .with(device, session_id, anything, anything)
        .and_return(custom_certificate_helper)
      expect(custom_certificate_helper).to receive(:install_ca_cert)
      expect(BrowserStack.logger).to receive(:info).with("Successfully installed CA certificate")

      # Call the actual certificate installation code from execute_fire_cmd
      # This is the actual implementation from the server.rb file
      if params.key?('customCertificateFile') && params['is_app_testing']
        begin
          filetype = JSON.parse(params['customCertificateFile'])['filetype']
          if filetype && ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
            custom_certificate_file = JSON.parse(params['customCertificateFile'])
            BrowserStack.logger.info("Certificate details: #{custom_certificate_file}")
            custom_cert_manager = CustomCertificate.new(
              device, session_id, custom_certificate_file, BrowserStack.logger
            )
            custom_cert_manager.install_ca_cert
            BrowserStack.logger.info("Successfully installed CA certificate")
          else
            # For PFX certificates
            BrowserStack.logger.info("Installing PFX Certificate")
            install_custom_pfx_certificate(device, params)
          end
        rescue StandardError => e
          # We're not testing error handling in this test
        end
      end
    end

    it 'should call install_ca_cert when filetype is certificates_cer_ios' do
      params['customCertificateFile'] = '{"s3_url":"https://example.com/cert.cer","filename":"cert.cer","filetype":"certificates_cer_ios","password":""}'

      expect(CustomCertificate).to receive(:new)
        .with(device, session_id, anything, anything)
        .and_return(custom_certificate_helper)
      expect(custom_certificate_helper).to receive(:install_ca_cert)
      expect(BrowserStack.logger).to receive(:info).with("Successfully installed CA certificate")

      # Call the actual certificate installation code from execute_fire_cmd
      if params.key?('customCertificateFile') && params['is_app_testing']
        begin
          filetype = JSON.parse(params['customCertificateFile'])['filetype']
          if filetype && ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
            custom_certificate_file = JSON.parse(params['customCertificateFile'])
            BrowserStack.logger.info("Certificate details: #{custom_certificate_file}")
            custom_cert_manager = CustomCertificate.new(
              device, session_id, custom_certificate_file, BrowserStack.logger
            )
            custom_cert_manager.install_ca_cert
            BrowserStack.logger.info("Successfully installed CA certificate")
          else
            # For PFX certificates
            BrowserStack.logger.info("Installing PFX Certificate")
            install_custom_pfx_certificate(device, params)
          end
        rescue StandardError => e
          # We're not testing error handling in this test
        end
      end
    end

    it 'should call install_pfx_certificate when filetype is not a CA certificate type' do
      params['customCertificateFile'] = '{"s3_url":"https://example.com/cert.pfx","filename":"cert.pfx","filetype":"certificates_pfx","password":"test"}'

      expect_any_instance_of(Object).to receive(:install_custom_pfx_certificate).with(device, params)
      expect(BrowserStack.logger).to receive(:info).with("Installing PFX Certificate")

      # Call the actual certificate installation code from execute_fire_cmd
      if params.key?('customCertificateFile') && params['is_app_testing']
        begin
          filetype = JSON.parse(params['customCertificateFile'])['filetype']
          if filetype && ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
            custom_certificate_file = JSON.parse(params['customCertificateFile'])
            BrowserStack.logger.info("Certificate details: #{custom_certificate_file}")
            custom_cert_manager = CustomCertificate.new(
              device, session_id, custom_certificate_file, BrowserStack.logger
            )
            custom_cert_manager.install_ca_cert
            BrowserStack.logger.info("Successfully installed CA certificate")
          else
            # For PFX certificates
            BrowserStack.logger.info("Installing PFX Certificate")
            install_custom_pfx_certificate(device, params)
          end
        rescue StandardError => e
          # We're not testing error handling in this test
        end
      end
    end

    it 'should handle error when CA certificate installation fails' do
      params['customCertificateFile'] = '{"s3_url":"https://example.com/cert.cer","filename":"cert.cer","filetype":"certificates_cer_android","password":""}'

      # Create a new instance of CustomCertificate that will raise an error
      custom_cert_manager = double('custom_cert_manager')
      expect(CustomCertificate).to receive(:new)
        .with(device, session_id, anything, anything)
        .and_return(custom_cert_manager)
      expect(custom_cert_manager).to receive(:install_ca_cert)
        .and_raise(StandardError.new("CA cert installation failed"))

      # Call the actual certificate installation code from execute_fire_cmd
      result = nil
      if params.key?('customCertificateFile') && params['is_app_testing']
        begin
          filetype = JSON.parse(params['customCertificateFile'])['filetype']
          if filetype && ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
            custom_certificate_file = JSON.parse(params['customCertificateFile'])
            BrowserStack.logger.info("Certificate details: #{custom_certificate_file}")
            custom_cert_manager = CustomCertificate.new(
              device, session_id, custom_certificate_file, BrowserStack.logger
            )
            custom_cert_manager.install_ca_cert
            BrowserStack.logger.info("Successfully installed CA certificate")
          else
            # For PFX certificates
            BrowserStack.logger.info("Installing PFX Certificate")
            install_custom_pfx_certificate(device, params)
          end
        rescue StandardError => e
          result = if e.message.include?("Invalid password")
                     [500, {
                       error: e.message,
                       kind: "certificate_install_invalid_password",
                       type: "user_error"
                     }.to_json]
                   else
                     [500, {
                       error: e.message,
                       kind: "browserstack_certificate_install_failure",
                       type: "browserstack_error"
                     }.to_json]
                   end
        end
      end

      expect(result).to be_a(Array)
      expect(result[0]).to eq(500)
      response = JSON.parse(result[1])
      expect(response["kind"]).to eq("browserstack_certificate_install_failure")
      expect(response["error"]).to eq("CA cert installation failed")
    end
  end

  context 'when testing the install_custom_pfx_certificate method' do
    let(:params) do
      {
        'device' => device,
        'automate_session_id' => session_id,
        'event_hash' => {},
        'customCertificateFile' => '{"s3_url":"https://example.com/cert.pfx","filename":"cert.pfx","filetype":"certificates_pfx","password":"test"}'
      }
    end

    it 'should call the CustomCertificate helper with correct parameters' do
      custom_cert_manager = double('custom_cert_manager')
      expect(CustomCertificate).to receive(:new)
        .with(device, session_id, anything, anything)
        .and_return(custom_cert_manager)
      expect(custom_cert_manager).to receive(:install_pfx_certificate)

      # Call the actual method
      install_custom_pfx_certificate(device, params)
    end

    it 'should handle errors and raise FireCMDException' do
      custom_cert_manager = double('custom_cert_manager')
      expect(CustomCertificate).to receive(:new)
        .with(device, session_id, anything, anything)
        .and_return(custom_cert_manager)
      expect(custom_cert_manager).to receive(:install_pfx_certificate)
        .and_raise(StandardError.new("PFX cert installation failed"))

      expect do
        install_custom_pfx_certificate(device, params)
      end.to raise_error(FireCMDException, /browserstack_certificate_install_failure/)
    end
  end
end

context "download_and_install_app function" do
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(nil)
    stub_const("STATE_FILES_DIR", __dir__)
  end
  let(:params) do
    {
      event_hash: {}
    }
  end
  let(:app_identifier) { "app_identifier" }
  let(:download_folder) { "/tmp/apps/#{app_identifier}" }

  def allow_download
    expect(FileUtils).to receive(:mkdir_p).with(download_folder).and_return(true)
    allow(SecureRandom).to receive(:uuid).and_return("uuid")
    expect(FileUtils).to receive(:touch).with("#{download_folder}/uuid.starting").and_return(true)
    expect(BrowserStack::HttpUtils).to receive(:download_file).with(any_args).and_return(true)
  end

  def install_app
    expect_any_instance_of(Object).to receive(:system).and_return(true)
    expect_any_instance_of(Object).to receive(:install_apps).and_return(["success", 0])
    expect(FileUtils).to receive(:touch).with(/.complete$/).and_return(true)
    expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
  end

  def simple_download_flow
    allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
    allow_any_instance_of(Object).to receive(:mark_event_start).with(any_args).and_return(true)
    allow_any_instance_of(Object).to receive(:mark_event_end).and_return(true)
    allow_download
    install_app
  end

  let(:test_params) do
    {
      package: "dummy_package",
      app_url: "https://d1f4l50qmgi5dp.cloudfront.net/08db046d94aa52c2d761320de7f046f0803af7fd/08db046d94aa52c2d761320de7f046f0803af7fd.apk?Expires=1598350523&Signature=Bd4l6ZJcgnwbuK5W3Eb~EcyHTam1OChdh2Pg-mPHwRQSBsUPc0JeMzJuyq6BRDveG2uIlxshNkPwbvv0061shSlRm2PbOxAud0kd4TplUn49KecTlQGcCUHSI3SXFJpFmVd0XjwNJ4hTHzFeokGcTX2SdFzsKVCQhp5Cq7V2f3UIibTBq0TU6pvi1Yum39GWB~gj6Vh7z4O7Nflcxn65cik2rtIKQWacxIh69omm2H5BfKBjsR~CHqKKNX4mj96Lq8ilqyTjXanO40AzO4n9kC3xQFVHWo0oco5AfAcHvUqtpm7D2smeHkCflXDzBmQIEN7tBtdraA5xN1Lg2U11vw__&Key-Pair-Id=APKAIZPU63JWJETND6AA",
      session_id: "session_id",
      download_params: {
        event_hash: {},
        app_type: "main"
      }
    }
  end

  context "download_apps" do
    it "should return download_path and uuid if download_apps is successful"\
       " and there are no any already_downloaded_apps" do
      allow_any_instance_of(Object).to receive(:check_for_already_downloaded_apps).and_return([nil, 0])
      allow_download
      received_download_path, received_uuid = download_apps(download_folder, app_identifier, test_params[:package],
                                                            test_params[:app_url], 30, test_params[:session_id],
                                                            test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_download_path).to eq(expected_app_path)
      expect(received_uuid).to eq("uuid")
    end

    it "should return download_path and uuid if download_apps is successful "\
       "and there are no any already_downloaded_apps for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow_any_instance_of(Object).to receive(:check_for_already_downloaded_apps).and_return([nil, 0])
      allow_download
      received_download_path, received_uuid = download_apps(download_folder, app_identifier, test_params[:package],
                                                            test_params[:app_url], 30, test_params[:session_id],
                                                            test_params[:download_params], 'apks')
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_download_path).to eq(expected_app_path)
      expect(received_uuid).to eq("uuid")
    end

    it "should return download_path and uuid if download_apps is successful and there are already_downloaded_apps" do
      allow_any_instance_of(Object).to receive(:check_for_already_downloaded_apps).and_return(["uuid1", 0])
      allow_any_instance_of(Object).to receive(:set_event_time).with('app_download_time', params[:event_hash], 0)
      received_download_path, received_uuid = download_apps(download_folder, app_identifier, test_params[:package],
                                                            test_params[:app_url], 30, test_params[:session_id],
                                                            test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid1.apk"
      expect(received_download_path).to eq(expected_app_path)
      expect(received_uuid).to eq("uuid1")
    end

    it "should return download_path and uuid if download_apps is successful "\
       "and there are already_downloaded_apps for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow_any_instance_of(Object).to receive(:check_for_already_downloaded_apps).and_return(["uuid1", 0])
      allow_any_instance_of(Object).to receive(:set_event_time).with('app_download_time', params[:event_hash], 0)
      received_download_path, received_uuid = download_apps(download_folder, app_identifier, test_params[:package],
                                                            test_params[:app_url], 30, test_params[:session_id],
                                                            test_params[:download_params], 'apks')
      expected_app_path = "/tmp/apps/app_identifier/uuid1.apks"
      expect(received_download_path).to eq(expected_app_path)
      expect(received_uuid).to eq("uuid1")
    end
  end

  context "get_err_msg_for_failed_injection" do
    it "will return generic message for nil failure reason" do
      msg = get_err_msg_for_failed_injection(nil, nil)
      expect(msg).to eq("Failed injecting the app")
    end
    it "will return biometric err message for proguarded failure reason" do
      msg = get_err_msg_for_failed_injection("proguarded_app", "biometrics")
      expect(msg).to eq("BIOMETRICS_INJECTING_FAILED_AS_PROGUARDED")
    end
    it "will return instrumented err message for proguarded failure reason" do
      msg = get_err_msg_for_failed_injection("proguarded_app", "instrumented")
      expect(msg).to eq("BIOMETRICS_INJECTING_FAILED_AS_PROGUARDED")
    end
    it "will return camera err message for proguarded failure reason" do
      msg = get_err_msg_for_failed_injection("proguarded_app", "camera")
      expect(msg).to eq("CAMERA_INJECTING_FAILED_AS_PROGUARDED")
    end
  end

  context "check_for_already_downloaded_apps" do
    it "should return uuid as nil and total_retries as 0 if download_folder doesn't exist" do
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id])
      expect(received_uuid).to eq(nil)
      expect(received_retries).to eq(0)
    end

    it "should return uuid as nil and total_retries as 0 if download_folder doesn't exist for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id])
      expect(received_uuid).to eq(nil)
      expect(received_retries).to eq(0)
    end

    it "should return uuid as nil and total_retries as 0 if download_folder "\
       "exist but not .complete file" do
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return(["/tmp/apps/app_identifier/uuid.apk"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(2).times.ordered.and_return([])
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id])
      expect(received_uuid).to eq(nil)
      expect(received_retries).to eq(2)
    end

    it "should return uuid as nil and total_retries as 0 if download_folder "\
       "exist but not .complete file for APKS files" do
      test_params[:download_params][:is_apks_archive] = true
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return(["/tmp/apps/app_identifier/uuid.apks"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(2).times.ordered.and_return([])
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id], "apks")
      expect(received_uuid).to eq(nil)
      expect(received_retries).to eq(2)
    end

    it "should return uuid if both download_folder and .complete file exist" do
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return(["/tmp/apps/app_identifier/uuid.apk"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      expect(Dir).to receive(:glob).with(/.complete$/).and_return(["/tmp/apps/app_identifier/uuid.complete"])
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id])
      expect(received_uuid).to eq("uuid")
      expect(received_retries).to eq(0)
    end

    it "should return uuid if both download_folder and .complete file exist for APKS apps" do
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return(["/tmp/apps/app_identifier/uuid.apks"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      expect(Dir).to receive(:glob).with(/.complete$/).and_return(["/tmp/apps/app_identifier/uuid.complete"])
      received_uuid, received_retries = check_for_already_downloaded_apps(download_folder, app_identifier,
                                                                          test_params[:session_id], "apks")
      expect(received_uuid).to eq("uuid")
      expect(received_retries).to eq(0)
    end
  end

  it "should raise FireCMDException if bad app_url" do
    expect do
      download_and_install_app(test_params[:package], "app_url",
                               true, 30, test_params[:session_id],
                               test_params[:download_params])
    end.to raise_error(FireCMDException)
  end

  context "If download folder is empty" do
    it "should download main app if no previous downloads found" do
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should download main app if no previous downloads found for APKS app" do
      test_params[:download_params][:is_apks_archive] = true
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should download test app if no previous downloads found" do
      test_params[:download_params][:app_type] = "test"
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should clear cached app data if install fails and raise AppInstallFailedException" do
      test_params[:download_params][:app_type] = "test"
      allow(File).to receive(:exist?).with(download_folder).and_return(false)
      allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
      allow_any_instance_of(Object).to receive(:mark_event_start).with(any_args).and_return(true)
      allow_any_instance_of(Object).to receive(:mark_event_end).and_return(true)
      expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
      allow_download

      expect_any_instance_of(Object).to receive(:install_apps).and_return(["Failure", 1])
      expect(FileUtils).to receive("rm_rf").with(download_folder).and_return([download_folder])

      expect do
        download_and_install_app(test_params[:package], test_params[:app_url],
                                 true, 30, test_params[:session_id],
                                 test_params[:download_params])
      end.to raise_error(AppInstallFailedException)
    end

    context "subregion app caching flow" do
      alias_method :function_alias, :download_and_install_app

      it "should clear cached app data and retry if install fails for subregion caching enabled" do
        test_params[:download_params][:app_type] = "main"
        test_params[:download_params][:subregion_app_caching_enabled] = true
        test_params[:download_params][:is_app_testing] = true
        allow(File).to receive(:exist?).with("/tmp/app_install_log_.log").and_return(false)
        allow(File).to receive(:exist?).with("/tmp/app_install_pid_").and_return(false)
        allow(File).to receive(:exist?).with(download_folder).and_return(false)
        allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
        allow_any_instance_of(Object).to receive(:mark_event_start).with(any_args).and_return(true)
        allow_any_instance_of(Object).to receive(:mark_event_end).and_return(true)
        expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
        allow_download

        expect_any_instance_of(Object).to receive(:install_apps).and_return(["INSTALL_PARSE_FAILED_NO_CERTIFICATES", 1])
        expect(FileUtils).to receive("rm_rf").with(download_folder).and_return([download_folder])

        expect_any_instance_of(Object).to receive(:download_and_install_app).with(
          anything, anything, anything, anything, anything,
          hash_including(subregion_app_caching_enabled: false), anything
        ).and_return(true)

        function_alias(test_params[:package], test_params[:app_url],
                       true, 30, test_params[:session_id],
                       test_params[:download_params])
      end
    end
  end

  context "If download folder is not empty" do
    it "should download main app if apk is found inside download folder but .complete file is not found" do
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return(["/tmp/apps/app_identifier/uuid.apk"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(2).times.ordered.and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should download main app if apk is found inside download folder but .complete file "\
       "is not found for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return(["/tmp/apps/app_identifier/uuid.apks"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(2).times.ordered.and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should not download main app if apk is found inside download folder "\
       "and .complete file is also found" do
      allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return(["/tmp/apps/app_identifier/uuid.apk"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      expect(Dir).to receive(:glob).with(/.complete$/).and_return(["/tmp/apps/app_identifier/uuid.complete"])

      expect(BrowserStack::HttpUtils).not_to receive(:download_file)
      allow_any_instance_of(Object).to receive(:set_event_time).with('app_download_time', params[:event_hash], 0)
      install_app

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should not download main app if apk is found inside download folder "\
       "and .complete file is also found for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return(["/tmp/apps/app_identifier/uuid.apks"])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      expect(Dir).to receive(:glob).with(/.complete$/).and_return(["/tmp/apps/app_identifier/uuid.complete"])

      expect(BrowserStack::HttpUtils).not_to receive(:download_file)
      allow_any_instance_of(Object).to receive(:set_event_time).with('app_download_time', params[:event_hash], 0)
      install_app

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should download the app if .apk and .starting is not found" do
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return([])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should download the app if .apks and .starting is not found for APKS apps" do
      test_params[:download_params][:is_apks_archive] = true
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return([])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should wait/retry for max 7 secs if any .starting file found but .apk and .complete file not found before "\
       "downloading the app" do
      allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apk$/).and_return([])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(7).times.ordered.and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apk"
      expect(received_app_path).to eq(expected_app_path)
    end

    it "should wait/retry for max 7 secs if any .starting file found but .apks and .complete file not found before "\
       "downloading the app for APKS files" do
      test_params[:download_params][:is_apks_archive] = true
      allow_any_instance_of(Object).to receive(:get_folder_path_details_from_s3_url).and_return("app_identifier")
      allow(File).to receive(:exist?).with(download_folder).and_return(true)
      expect(Dir).to receive(:glob).with(/.apks$/).and_return([])
      expect(Dir).to receive(:glob).with(/.starting$/).and_return(["/tmp/apps/app_identifier/uuid.starting"])
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(Dir).to receive(:glob).with(/.complete$/).exactly(7).times.ordered.and_return([])
      simple_download_flow

      received_app_path = download_and_install_app(test_params[:package], test_params[:app_url],
                                                   true, 30, test_params[:session_id],
                                                   test_params[:download_params])
      expected_app_path = "/tmp/apps/app_identifier/uuid.apks"
      expect(received_app_path).to eq(expected_app_path)
    end
  end

  it "raise Download Exception" do
    allow(BrowserStack::HttpUtils).to receive(:download_file).and_raise("Fatal Error")
    expect do
      download_and_install_app(test_params[:package], test_params[:app_url],
                               true, 30, test_params[:session_id],
                               test_params[:download_params])
    end.to raise_error(FireCMDException)
  end

  it "raises other apps install exception" do
    other_apps = [
      {
        "bundle_id" => "123",
        "download_url" => "some_url",
        "download_timeout" => 30
      }
    ]
    allow(Object).to receive(:download_and_install_app).and_raise("Fatal Error")

    expect do
      install_dependent_apps(other_apps, "session_id", "false", { "user_id" => "user_id" })
    end.to raise_error(FireCMDException)
  end
end

describe 'Async App Download and Install Thread' do
  let(:params) { { "s3_app_url" => "https://example.com/app.apk", "async_app_download_install" => true } }
  let(:sess_details) { double("sess_details") }
  let(:download_and_install_thread) { instance_double(Thread, join: true) }

  before do
    allow(BrowserStack.logger).to receive(:info)
    allow(Thread).to receive(:bs_run).and_yield
    allow(self).to receive(:install_app_firecmd)
  end

  context 'when conditions are met' do
    let(:is_app_testing) { true }

    it 'creates a new thread and calls install_app_firecmd' do
      expect(Thread).to receive(:bs_run)
      expect(self).to receive(:install_app_firecmd).with(
        params, sess_details,
        hash_including(is_app_testing: is_app_testing)
      )

      if is_app_testing && params["s3_app_url"] && params["async_app_download_install"]
        Thread.bs_run do
          install_app_firecmd(params, sess_details, { is_app_testing: is_app_testing })
        end
      end
    end
  end

  context 'when conditions are not met' do
    let(:is_app_testing) { false }

    it 'does not create a new thread or call install_app_firecmd' do
      expect(Thread).not_to receive(:bs_run)
      expect(self).not_to receive(:install_app_firecmd)

      if is_app_testing && params["s3_app_url"] && params["async_app_download_install"]
        Thread.bs_run do
          install_app_firecmd(params, sess_details, { is_app_testing: is_app_testing })
        end
      end
    end
  end
end

describe "Custom Media" do
  let(:device) { "ABCDEFGH" }
  let(:custom_media_manager) { CustomMediaManager.new('1234', @feature_usage, SERVER_LOG, @mock_logger) }
  let(:params) do
    {
      device: device,
      automate_session_id: '123',
      event_hash: {},
      'custom_media' => "
         [
           \"{\\\"filename\\\": \\\"file1\\\", \\\"filetype\\\": \\\"image\\\", "\
          "\\\"s3_url\\\": \\\"https://thisisrandomurl1.com\\\" }\",
           \"{\\\"filename\\\": \\\"file2\\\", \\\"filetype\\\": \\\"image\\\", "\
          "\\\"s3_url\\\": \\\"https://thisisrandomurl2.com\\\" }\"
         ]
      "
    }
  end

  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:fatal)
    allow(@mock_logger).to receive(:error)
    allow_any_instance_of(BrowserStack::AndroidDevice)
      .to receive(:load_config)
      .and_return(
        # Must stub non-empty config otherwise AndroidDevice will raise exception
        JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']['test_device']
      )

    allow_any_instance_of(BrowserStack::AndroidDevice).to receive(:pm_clear_command_fails?).and_return(true)
  end

  it "raises exception if download fails" do
    allow(BrowserStack::HttpUtils).to receive(:download_file).and_raise("Fatal Error")
    expect { custom_media_manager.download_and_push(params) }.to raise_error(FireCMDException)
  end

  it "raises exception if download passed but push failed" do
    allow(custom_media_manager).to receive(:push_media_file_to_device)
      .and_raise(FireCMDException.new("Custom media Push failed"))

    expect { custom_media_manager.download_and_push(params) }.to raise_error(FireCMDException)
  end
end

describe 'check_and_push_failure_reason' do
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:fatal)
    stub_const("STATE_FILES_DIR", __dir__)
    @params = {
      edsHost: 'edsHost',
      edsPort: 'edsPort',
      edsKey: 'edsKey',
      genre: 'app_automate',
      automate_session_id: "session_id",
      last_request: "last_request",
      last_raw_log: "last_raw_log",
      reason: "reason",
      session_limit: "7200"
    }
  end

  it 'should push valid data to eds and zombie for sotimout' do
    appium_log_path = "#{__dir__}/../fixtures/appium_command_timeout.log"
    expected_reponse = "{\"s\"=>{:last_request=>\"last_request\", :reason=>\"reason\", :last_raw_log=>"\
                       "\"last_raw_log\", :session_limit=>\"7200\"}, \"v\"=>[], \"r\"=>\"SOTIMEOUT-browserstack-"\
                       "command_timeout_threshold_reached\", \"f\"=>\"false\"}"
    expect_any_instance_of(Object)
      .to receive(:zombie_push).with('android', "app-SOTIMEOUT-browserstack", "app_automate", "",
                                     expected_reponse, "", "session_id", nil, "false")
    expect_any_instance_of(EDS)
      .to receive(:push_logs).with(
        EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
        { "hashed_id" => "session_id",
          "secondary_diagnostic_reason" => "SOTIMEOUT-browserstack-command_timeout_threshold_reached",
          "product" => { "stability" => { "reason" => "error-socket-timeout" } } }
      )
    expect_any_instance_of(Object).to receive(:session_appium_logs).with("device", "session_id", true)
                                                                   .and_return(appium_log_path)
    check_and_push_failure_reason("device", @params, true)
  end

  it 'should push valid data to eds and zombie for sotimout in case of missing params from rails' do
    appium_log_path = "#{__dir__}/../fixtures/appium_command_timeout.log"
    @params.delete(:last_raw_log)
    expected_reponse = "{\"s\"=>{:last_request=>\"last_request\", :reason=>\"reason\", :last_raw_log=>nil, "\
                       ":session_limit=>\"7200\"}, \"v\"=>[], \"r\"=>\"SOTIMEOUT-browserstack-command_timeout_"\
                       "threshold_reached\", \"f\"=>\"false\"}"
    expect_any_instance_of(Object)
      .to receive(:zombie_push).with('android', "app-SOTIMEOUT-browserstack", "app_automate", "",
                                     expected_reponse, "", "session_id", nil, "false")
    expect_any_instance_of(EDS)
      .to receive(:push_logs)
      .with(EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
            { "hashed_id" => "session_id",
              "secondary_diagnostic_reason" => "SOTIMEOUT-browserstack-command_timeout_threshold_reached",
              "product" => { "stability" => { "reason" => "error-socket-timeout" } } } )
    expect_any_instance_of(Object).to receive(:session_appium_logs).with("device", "session_id", true)
                                                                   .and_return(appium_log_path)
    check_and_push_failure_reason("device", @params, true)
  end

  it 'should push valid data to eds and zombie in case of exception raised for sotimeout' do
    expect_any_instance_of(Object).to receive(:zombie_push).with('android', "app-SOTIMEOUT-check-failed",
                                                                 "app_automate", anything, anything, anything,
                                                                 "session_id", nil)
    check_and_push_failure_reason("device", @params, true)
  end
end

describe 'POST /adb' do
  let(:adb) { double('abd') }
  before(:each) do
    allow_any_instance_of(Object).to receive(:check_basic_auth)
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
  end

  it 'should execute raw adb command as passed in POST body' do
    expect(adb).to receive(:execute).with("timeout", "0", "/path/to/adb", "--version", "2>&1").and_return('1.0.0')

    post '/adb', arguments: 'timeout 0 /path/to/adb --version 2>&1'
    expect(last_response.status).to eq(200)
    body = JSON.parse(last_response.body)
    expect(body['command_output']).to eq('1.0.0')
  end

  it 'should return 500 if error occurs' do
    allow(adb).to receive(:execute).and_raise("boom")

    post '/adb', arguments: '--version'
    expect(last_response.status).to eq(500)
    body = JSON.parse(last_response.body)
    expect(body['error']).to eq("boom")
  end
end

describe 'POST /launch_detox_instrumentation' do
  let(:adb) { double('abd') }
  let(:device) { "test_device" }
  let(:session_id) { "session_id" }
  let(:detox_helper) { double("detox_helper") }

  let(:state_files_dir) do
    BrowserStack::STATE_FILES_DIR.to_s
  end

  let(:minimized_cleanup_reserved_file) do
    "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
  end

  let(:preserve_app_state_reserved_file) do
    "#{state_files_dir}/preserve_app_state_reserved_#{device}"
  end

  let(:browserstack_watcher_helper) { double(WatcherHelper) }

  before(:each) do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")

    ModelDatabaseMock = double('ModelDatabaseMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    AndroidToolkitMock = double('AndroidToolkitMock') # rubocop:todo Lint/ConstantDefinitionInBlock

    allow(ModelDatabaseMock).to receive(:property).and_return false
    allow(ModelDatabase).to receive(:new).and_return ModelDatabaseMock

    allow(AndroidToolkitMock).to receive(:devices_info).and_return "online"
    allow(AndroidToolkit::ADB).to receive(:new).and_return AndroidToolkitMock

    PerformanceStatisticsMock = double('PerformanceStatisticsMock') # rubocop:todo Lint/ConstantDefinitionInBlock
    allow(PerformanceStatisticsMock).to receive(:start).and_return true
    allow(PerformanceStatistics).to receive(:new).and_return PerformanceStatisticsMock
    allow(WatcherHelper).to receive(:new).and_return browserstack_watcher_helper
    allow(browserstack_watcher_helper).to receive(:dump_logs)

    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow(OSUtils).to receive(:execute).with(/lsof/).and_return 384
    allow(AppInjection).to receive(:app_patching_enabled_in_app_automate?).and_return false

    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow_any_instance_of(Object).to receive(:pre_start).and_return true
    allow_any_instance_of(Object).to receive(:get_default_appium_version).and_return "1.17.0"
    allow_any_instance_of(Object).to receive(:get_stream_width).and_return 384
    allow_any_instance_of(EDS).to receive(:push_logs)

    minimized_cleanup_reserved_file = "#{state_files_dir}/minimized_cleanup_reserved_#{device}"
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
    allow(DetoxHelper).to receive(:new).and_return(detox_helper)
  end

  it 'should successfully launch detox instrumentation process' do
    expect(detox_helper).to receive(:start_detox_client_instrumentation).and_return(nil)
    expect(detox_helper).to receive(:verify_app_launch).and_return(true)

    get "/launch_detox_instrumentation?device=#{device}", { session_id: session_id }.to_json

    expect(last_response.status).to eq(200)
    body = JSON.parse(last_response.body)
    expect(body['success']).to eq(true)
  end

  it 'could not launch detox instrumentation process' do
    expect(detox_helper).to receive(:start_detox_client_instrumentation).and_return(nil)
    expect(detox_helper).to receive(:verify_app_launch).and_return(false)

    get "/launch_detox_instrumentation?device=#{device}", { session_id: session_id }.to_json

    expect(last_response.status).to eq(200)
    body = JSON.parse(last_response.body)
    expect(body['success']).to eq(false)
  end

  it 'should catch exceptions and return 500' do
    expect(detox_helper).to receive(:start_detox_client_instrumentation).and_return(nil)
    expect(detox_helper).to receive(:verify_app_launch).and_raise("Something went Wrong")

    get "/launch_detox_instrumentation?device=#{device}", { session_id: session_id }.to_json

    expect(last_response.status).to eq(500)
    body = JSON.parse(last_response.body)
    expect(body['success']).to eq(false)
  end
end

describe 'GET /toggle_voice_over' do
  let(:req_body_start) do
    '{ "device": "random_device_id", "enabled": true }'
  end
  let(:json_req_body_start) do
    JSON.parse(req_body_start)
  end

  let(:req_body_stop) do
    '{ "device": "random_device_id", "enabled": false }'
  end
  let(:json_req_body_stop) do
    JSON.parse(req_body_stop)
  end

  let(:talkback_helper) { double('talkback_helper') }

  it 'should start_talkback if enabled is true' do
    allow(JSON).to receive(:parse).and_return(json_req_body_start)
    expect(TalkbackHelper).to receive(:new).and_return(talkback_helper)
    expect(talkback_helper).to receive(:start_talkback)
    expect(talkback_helper).not_to receive(:stop_talkback)

    get '/toggle_voice_over', req_body_start
  end

  it 'should stop_talkback if enabled is false' do
    allow(JSON).to receive(:parse).and_return(json_req_body_stop)
    expect(TalkbackHelper).to receive(:new).and_return(talkback_helper)
    expect(talkback_helper).not_to receive(:start_talkback)
    expect(talkback_helper).to receive(:stop_talkback)

    get '/toggle_voice_over', req_body_stop
  end
end

describe 'POST /set_device_state' do
  context "Parameters are invalid" do
    context "current and requested device state is same" do
      let(:request_body) { { device: "abcd", device_state: "0" } }
      it "Should return 422 with Invalid Parameters" do
        allow_any_instance_of(Object).to receive(:get_device_state).and_return("0")
        post '/set_device_state', request_body.to_json
        expect(last_response.status).to eq(422)
        expect(last_response.body).to include("Invalid Parameters")
      end
    end

    context "requested device state is invalid" do
      let(:request_body) { { device: "abcd", device_state: "random_state" } }
      it "Should return 422 with Invalid Parameters" do
        allow_any_instance_of(Object).to receive(:get_device_state).and_return("0")
        post '/set_device_state', request_body.to_json
        expect(last_response.status).to eq(422)
        expect(last_response.body).to include("Invalid Parameters")
      end
    end

    context "device id is nil" do
      let(:request_body) { { device: nil, device_state: "3" } }
      it "Should return 422 with Invalid Parameters" do
        allow_any_instance_of(Object).to receive(:get_device_state).and_return("0")
        post '/set_device_state', request_body.to_json
        expect(last_response.status).to eq(422)
        expect(last_response.body).to include("Invalid Parameters")
      end
    end
  end

  context "Parameters are valid" do
    let(:request_body) { { device: "abcd", device_state: "3" } }
    before do
      @mock_android_device = double('AndroidDevice')
      allow(BrowserStack::AndroidDevice).to receive(:new).and_return(@mock_android_device)
    end

    context "Device is not a foldable device" do
      it "Should return 422 with is not a foldable device" do
        allow_any_instance_of(Object).to receive(:get_device_state).and_return("0")
        allow(@mock_android_device).to receive(:foldable_device?).and_return(false)
        post '/set_device_state', request_body.to_json
        expect(last_response.status).to eq(422)
        expect(last_response.body).to include("is not a foldable device")
      end
    end

    context "Device is a foldable device" do
      before do
        # allow_any_instance_of(Object).to receive(:get_device_state).and_return("0", "3")
        allow(@mock_android_device).to receive(:foldable_device?).and_return(true)
        allow(@mock_android_device).to receive(:model).and_return("SM-F1234")
        allow_any_instance_of(Object).to receive(:toggle_device_screen)
      end

      context "Device state has been updated to the required state" do
        it "Should return 200 with completed" do
          allow_any_instance_of(Object).to receive(:get_device_state).and_return("0", "3")
          post '/set_device_state', request_body.to_json
          expect(last_response.status).to eq(200)
          expect(last_response.body).to include("completed")
        end
      end

      context "Device state has not been updated to the required state" do
        it "Should return 500 with proper error message" do
          allow_any_instance_of(Object).to receive(:get_device_state).and_return("0", "0")
          post '/set_device_state', request_body.to_json
          expect(last_response.status).to eq(500)
          expect(last_response.body).to include("is not updated to state")
        end
      end
    end
  end
end

describe '/driver_actions routes' do
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  context 'request from outside localhost' do
    it 'returns 404 as route should not be visible to outside world' do
      get '/driver_actions'

      expect(last_response.status).to eq(404)
    end
  end

  describe '/:device_id/notify_cleanup_done' do
    before(:each) do
      @request_host = 'localhost'
    end

    context 'device not listed as exception' do
      it 'should send cleanup done request to rails if device is not listed in exceptions' do
        allow_any_instance_of(StaticConf::Config).to receive(:device_rails_endpoint).and_return("myrailsapp.com")

        stub_request(:get, 'myrailsapp.com/admin/cleanup_finished?instance_id=1234')
          .to_return(status: 200, body: 'OK')
        get '/driver_actions/1234/notify_cleanup_done'

        expect(last_response.status).to eq(200)
        body = JSON.parse(last_response.body)
        expect(body['status']).to eq(200)
        expect(body['data']['rails_response_status']).to eq("200")
        expect(body['data']['rails_response_body']).to eq('OK')
      end
    end

    context 'device is listed as exception' do
      it 'should send cleanup done request to exception endpoint if device is listed as one' do
        allow_any_instance_of(StaticConf::Config).to receive(:device_rails_endpoint).and_return("myexceptionsapp.com")
        stub_request(:get, 'myexceptionsapp.com/admin/cleanup_finished?instance_id=1234')
          .to_return(status: 200, body: "OK")
        get '/driver_actions/1234/notify_cleanup_done'

        expect(last_response.status).to eq(200)
        body = JSON.parse(last_response.body)
        expect(body['status']).to eq(200)
        expect(body['data']['rails_response_status']).to eq("200")
        expect(body['data']['rails_response_body']).to eq('OK')
      end
    end

    context 'request to rails/exceptions fails' do
      it 'still returns 200 response and gives details of error and status code for rails request' do
        allow_any_instance_of(StaticConf::Config).to receive(:device_rails_endpoint).and_return("myrailsapp.com")
        stub_request(:get, 'myrailsapp.com/admin/cleanup_finished?instance_id=1234')
          .to_return(status: 500, body: "Error")
        get '/driver_actions/1234/notify_cleanup_done'

        expect(last_response.status).to eq(200)
        body = JSON.parse(last_response.body)
        expect(body['status']).to eq(200)
        expect(body['data']['rails_response_status']).to eq("500")
        expect(body['data']['rails_response_body']).to eq('Error')
      end
    end
  end
end

describe 'device_proxy_required?' do
  before do
    allow_any_instance_of(BrowserStack::AndroidDevice)
      .to receive(:load_config)
      .and_return(
        # Must stub non-empty config otherwise AndroidDevice will raise exception
        JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']['test_device']
      )

    allow_any_instance_of(BrowserStack::AndroidDevice).to receive(:rooted?).and_return(true)
  end

  it 'should return true if Samsung Galaxy Note 3 is device_name' do
    params = {
      "device" => "test_device",
      "genre" => "automate",
      "version" => "Samsung Galaxy Note 3-4.3"
    }
    result = device_proxy_required?(params, 4)
    expect(result).to eql(true)
  end

  context 'when the device is non-rooted and the browser is firefox' do
    before do
      allow_any_instance_of(BrowserStack::AndroidDevice).to receive(:rooted?).and_return(false)
    end

    it 'returns true' do
      params = {
        "device" => "test_device",
        "device_browser" => "firefox",
        "genre" => "live"
      }
      result = device_proxy_required?(params, 5)
      expect(result).to eql(true)
    end
  end

  it 'should return true if genre is live_testing for samsung browser' do
    params = {
      "device" => "test_device",
      "genre" => "live_testing",
      "device_browser" => "internet",
      "version" => "Samsung Galaxy Note 4-4.4"
    }
    result = device_proxy_required?(params, 4)
    expect(result).to eql(true)
  end

  it 'should return false if genre is live_testing for chrome browser and device is something other than Note 3' do
    params = {
      "device" => "test_device",
      "genre" => "live_testing",
      "device_browser" => "chrome",
      "version" => "Samsung Galaxy Note 4-4.4"
    }
    result = device_proxy_required?(params, 4)
    expect(result).to eql(false)
  end

  it 'should return false if genre is automate and device version is 5' do
    params = {
      "device" => "test_device",
      "genre" => "automate",
      "version" => "Samsung Galaxy S6-5.0"
    }
    result = device_proxy_required?(params, 5)
    expect(result).to eql(false)
  end

  # "doNotSetProxy" was introduced to disable privoxy in case a user in automate session passed
  # --host-resolver-rules arg in chromeOptions.
  # This was because, the arg '--proxy-server' and "--host-resoler-rules" conflicted with each other.
  # It will not affect any live/app live/app automate session
  it 'should return false straightaway if the param "doNotSetProxy" exists with value true' do
    params = {
      "device" => "test_device",
      "genre" => "COULD_BE_ANY",
      "version" => "DOESN'T_MATTER",
      "doNotSetProxy" => true
    }
    result = device_proxy_required?(params, 4)
    expect(result).to eql(false)
  end
end

describe 'DELETE /recycle' do
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  it "should return 200 if it's a VM" do
    allow(OSUtils).to receive(:virtual_machine?).and_return(true)
    expect(OSUtils).to receive(:shutdown_machine)

    delete '/recycle'

    expect(last_response).to be_ok
    expect(last_response.body).to be_empty
  end

  it "should return error if itsn't a VM" do
    allow(OSUtils).to receive(:virtual_machine?).and_return(false)

    delete '/recycle'

    expect(OSUtils).to_not receive(:shutdown_machine)
    expect(last_response).not_to be_ok
    expect(last_response.body).to include("Can't recycle")
  end
end

describe 'should_stop_vpn?' do
  before do
    allow(@mock_logger).to receive(:info)

    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
    @device = "pixel_3_device_id"

    $mobile_db_fixture.db[:models].insert(
      name: 'Pixel 3',
      version: "9.0",
      bsrun_device: true,
      uses_wifi: false
    )

    $mobile_db_fixture.db[:models].insert(
      name: 'HD1900', # OnePlus, vpn exception example
      version: "9.0",
      bsrun_device: true,
      uses_wifi: true
    )

    $mobile_db_fixture.db[:models].insert(
      name: 'non_bsrun_model', # Non-bsrun example
      version: "8.0",
      bsrun_device: false,
      uses_wifi: false
    )
  end

  context 'when local is not enabled' do
    it 'always returns false' do
      local_enabled = false
      expect(should_stop_vpn?(local_enabled, "firefox")).to eql(false)
      expect(should_stop_vpn?(local_enabled, "ucmobile")).to eql(false)
      expect(should_stop_vpn?(local_enabled, "chrome")).to eql(false)
    end
  end
  context 'when using chrome browser' do
    it 'always returns false' do
      expect(should_stop_vpn?(true, "chrome")).to eql(false)
      expect(should_stop_vpn?(false, "chrome")).to eql(false)
    end
  end

  context 'when using firefox browser' do
    context 'when the device is a bsrun device' do
      it 'returns false' do
        expect(should_stop_vpn?(false, "chrome")).to eql(false)
      end
    end

    context 'when the device is not a bsrun device, not a vpn exception device and local is enabled' do
      before do
        @device = "non_bsrun_device_id"
      end

      it 'returns true' do
        expect(should_stop_vpn?(true, "firefox")).to eql(true)
      end
    end
  end

  context 'when using ucbrowser or samsung browser' do
    context 'when the device is not a vpn exeption device and local is enabled' do
      before do
        @device = "pixel_3_device_id"
      end

      it 'returns true' do
        expect(should_stop_vpn?(true, "ucmobile")).to eql(true)
        expect(should_stop_vpn?(true, "internet")).to eql(true)
      end
    end
  end

  context 'when using a vpn exception device' do
    before do
      @device = "oneplus7_device_id" # vpn exception device, uses wifi always
    end

    it 'always returns false' do
      expect(should_stop_vpn?(false, "firefox")).to eql(false)
      expect(should_stop_vpn?(true, "chrome")).to eql(false)
      expect(should_stop_vpn?(true, "internet")).to eql(false)
    end
  end
end

describe "feature usage" do
  it "initializes feature usage" do
    features_to_track = ["airplaneMode", "playstoreLogin", "customMedia", "gpsLocation", "geoLocation",
                         "customNetwork", "language", "locale"]

    initialize_feature_usage

    expect(@feature_usage).not_to be_nil
    features_to_track.each do |feature|
      expect(@feature_usage[feature][:success]).to eq("disabled")
    end
  end

  it "update feature usage" do
    feature = "some_feature"
    value = "some value"
    error = "some error"

    initialize_feature_usage
    update_feature_usage(feature, value, error)

    expect(@feature_usage[feature][:success]).to eq(value)
    expect(@feature_usage[feature][:exception]).to eq(error)
  end
end

describe "is_automate_or_app_automate" do
  it "should return true for  genre = selenium" do
    genre = "selenium"

    value = is_automate_or_app_automate(genre)

    expect(value).to be(true)
  end

  it "should return true for  genre = app_automate" do
    genre = "app_automate"

    value = is_automate_or_app_automate(genre)

    expect(value).to be(true)
  end

  it "should return false for  genre = app_live" do
    genre = "app_live"

    value = is_automate_or_app_automate(genre)

    expect(value).to be(false)
  end
end

describe "enable_google_api?" do
  let(:enable_play_apis_true) { { "enable_play_apis" => true } }
  let(:enable_play_apis_false) { { "enable_play_apis" => false } }
  let(:params_with_enable_google_api) { { "app_automate_custom_params" => enable_play_apis_true } }
  let(:params_without_enable_google_api) { { "app_automate_custom_params" => enable_play_apis_false } }
  let(:params_without_app_automate_custom_params) { { "app" => "123" } }

  context "when app_automate_custom_params cap is not present" do
    it "return false" do
      expect(enable_google_api?(params_without_app_automate_custom_params)).to be(false)
    end
  end

  context "when enable_play_apis flag is false and app_automate_custom_params cap is present" do
    it "return false" do
      expect(enable_google_api?(params_without_enable_google_api)).to be(false)
    end
  end

  context "when enable_play_apis flag is true" do
    it "return true" do
      expect(enable_google_api?(params_with_enable_google_api)).to be(true)
    end
  end
end

describe "get_folder_path_details_from_s3_url" do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  let(:url) do
    "https://d1f4l50qmgi5dp.cloudfront.net/08db046d94aa52c2d761320de7f046f0803af7fd/08db046d94aa52c2d761320de7f046f"\
    "0803af7fd.apk?Expires=1598350523&Signature=Bd4l6ZJcgnwbuK5W3Eb~EcyHTam1OChdh2Pg-mPHwRQSBsUPc0JeMzJuyq6BRDveG2uIl"\
    "xshNkPwbvv0061shSlRm2PbOxAud0kd4TplUn49KecTlQGcCUHSI3SXFJpFmVd0XjwNJ4hTHzFeokGcTX2SdFzsKVCQhp5Cq7V2f3UIibTBq0TU6"\
    "pvi1Yum39GWB~gj6Vh7z4O7Nflcxn65cik2rtIKQWacxIh69omm2H5BfKBjsR~CHqKKNX4mj96Lq8ilqyTjXanO40AzO4n9kC3xQFVHWo0oco5AfA"\
    "cHvUqtpm7D2smeHkCflXDzBmQIEN7tBtdraA5xN1Lg2U11vw__&Key-Pair-Id=APKAIZPU63JWJETND6AA"
  end

  let(:another_url) do
    "https://d1f4l50qmgi5dp.cloudfront.net/data/08db046d94aa52c2d761320de7f046f0803af7fd/08db046d94aa52c2d761320de7f04"\
    "6f0803af7fd.apk?Expires=1598350523&Signature=Bd4l6ZJcgnwbuK5W3Eb~EcyHTam1OChdh2Pg-mPHwRQSBsUPc0JeMzJuyq6BRDveG2uI"\
    "lxshNkPwbvv0061shSlRm2PbOxAud0kd4TplUn49KecTlQGcCUHSI3SXFJpFmVd0XjwNJ4hTHzFeokGcTX2SdFzsKVCQhp5Cq7V2f3UIibTBq0TU6"\
    "pvi1Yum39GWB~gj6Vh7z4O7Nflcxn65cik2rtIKQWacxIh69omm2H5BfKBjsR~CHqKKNX4mj96Lq8ilqyTjXanO40AzO4n9kC3xQFVHWo0oco5AfA"\
    "cHvUqtpm7D2smeHkCflXDzBmQIEN7tBtdraA5xN1Lg2U11vw__&Key-Pair-Id=APKAIZPU63JWJETND6AA"
  end

  let(:malformed_url) { "https://abcdp.xyz.net" }

  it "should return app identifier" do
    received_app_identifier = get_folder_path_details_from_s3_url(url)
    expected_app_identifier = "08db046d94aa52c2d761320de7f046f0803af7fd"
    expect(expected_app_identifier).to eq(received_app_identifier)
  end

  it "should return app identifier" do
    received_app_identifier = get_folder_path_details_from_s3_url(another_url)
    expected_app_identifier = "08db046d94aa52c2d761320de7f046f0803af7fd"
    expect(expected_app_identifier).to eq(received_app_identifier)
  end

  it "should return app identifier when skip_resign is set to true" do
    received_app_identifier = get_folder_path_details_from_s3_url(url, true)
    expected_app_identifier = "08db046d94aa52c2d761320de7f046f0803af7fd_unsigned"
    expect(expected_app_identifier).to eq(received_app_identifier)
  end

  it "should return app identifier when skip_resign is set to false" do
    received_app_identifier = get_folder_path_details_from_s3_url(url, false)
    expected_app_identifier = "08db046d94aa52c2d761320de7f046f0803af7fd"
    expect(expected_app_identifier).to eq(received_app_identifier)
  end

  it "should return app identifier when skip_resign is set to nil" do
    received_app_identifier = get_folder_path_details_from_s3_url(url, nil)
    expected_app_identifier = "08db046d94aa52c2d761320de7f046f0803af7fd"
    expect(expected_app_identifier).to eq(received_app_identifier)
  end

  it "should raise an error: app_identifier is nil from s3 url" do
    expect do
      get_folder_path_details_from_s3_url(malformed_url)
    end.to raise_error("app_identifier is nil from s3 url")
  end
end

describe "get_app_live_instrumentation_type" do
  it "should return screencap if backfill params are set" do
    params = { backfill: { "screencapture" => "true" } }
    expect(get_app_live_instrumentation_type(params)).to eq("screencap")
  end

  it "should return biometric if biometric param is set" do
    params = { is_biometric_toggled: "true" }
    expect(get_app_live_instrumentation_type(params)).to eq("biometrics")
  end

  it "should return biometric if biometric_hash is set and not nil" do
    params = { biometric_hash: {} }
    expect(get_app_live_instrumentation_type(params)).to eq("biometrics")
  end

  it "should return camera if camera param is set" do
    params = { is_camera_toggled: "true" }
    expect(get_app_live_instrumentation_type(params)).to eq("camera")
  end

  it "should return instrumented if both biometric and camera are set" do
    params = { is_camera_toggled: "true", is_biometric_toggled: "true" }
    expect(get_app_live_instrumentation_type(params)).to eq("instrumented")
  end

  it "should return mobileData if mobile data is set" do
    params = { is_mobile_data_toggled: "true" }
    expect(get_app_live_instrumentation_type(params)).to eq("mobileData")
  end

  it "should return none if no params are set" do
    expect(get_app_live_instrumentation_type({})).to eq("none")
  end
end

describe "get_snapshot_details_uiautomator2", focus: true do
  before do
    allow_any_instance_of(Object).to receive(:script_logger_args).and_return('random_thing')
    allow(@mock_logger).to receive(:info)
    allow(self).to receive(:system)
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
  end

  it "should raise StandardError if screenshot file or XML source file does not exist" do
    expect do
      get_snapshot_details_uiautomator2("oneplus7_device_id", {}, 20)
    end.to raise_error(StandardError, "UI Inspect failed using appium's uiautomator2")
  end

  it "should return screenshot and xml source if appium's uiautomator2 fetches XML and screenshot" do
    file = double
    dummy_xml = "<dummy_node></dummy_node>"
    allow_any_instance_of(Object).to receive(:`).and_return(dummy_xml)
    allow(File).to receive(:open).with("/tmp/snapshot_oneplus7_device_id/encoded_screenshot", "rt").and_return(file)
    expect(file).to receive(:read).and_return("DUMMY_SCREENSHOT")
    expect(File).to receive(:file?).twice.and_return(true)
    resp = get_snapshot_details_uiautomator2("oneplus7_device_id", {}, 20)
    expect(resp[:screenshot]).to eq("DUMMY_SCREENSHOT")
    expect(resp[:xmltree]).to eq("<dummy_node xpath=\"/dummy_node\"/>")
  end
end

describe "get_snapshot_details_wrapper" do
  before do
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
  end

  context 'When uiautomator1 is used' do
    it "should return response if get_snapshot_details is successfull" do
      stub(:get_snapshot_details) { { screenshot: "screenshot", xmltree: "xmltree" } }
      allow(FileUtils).to receive("rm_rf").and_return(["/tmp/snapshot_device"])
      res = get_snapshot_details_wrapper("oneplus7_device_id", {}, false)
      expect(res[:screenshot]).to eq("screenshot")
      expect(res[:xmltree]).to eq("xmltree")
    end

    it "should raise error if get_snapshot_details raises error" do
      stub(:get_snapshot_details).and_raise("get_snapshot_details error")
      allow(FileUtils).to receive("rm_rf").and_return(["/tmp/snapshot_device"])
      expect { get_snapshot_details_wrapper("oneplus7_device_id", {}, false) }
        .to raise_error("get_snapshot_details error")
    end
  end

  context 'When uiautomator2 is used' do
    it "should execute get_snapshot_details_uiautomator2 and return a response" do
      stub(:get_snapshot_details_uiautomator2) { { screenshot: "screenshot", xmltree: "xmltree" } }
      allow(FileUtils).to receive("rm_rf").and_return(["/tmp/snapshot_device"])
      res = get_snapshot_details_wrapper("oneplus7_device_id", {}, true)
      expect(res[:screenshot]).to eq("screenshot")
      expect(res[:xmltree]).to eq("xmltree")
    end

    it "should not execute with uiautomator2 when android version is less than 5" do
      stub(:get_snapshot_details) { { screenshot: "screenshot", xmltree: "xmltree" } }
      allow(FileUtils).to receive("rm_rf").and_return(["/tmp/snapshot_device"])
      res = get_snapshot_details_wrapper("tab_s4_device_id", {}, true)
      expect(res[:screenshot]).to eq("screenshot")
      expect(res[:xmltree]).to eq("xmltree")
    end
  end
end

describe "get_default_appium_version" do
  it "should return default appium version for os_version 4.4" do
    obtained_result = get_default_appium_version("4.4")
    expect(obtained_result).to eq("1.6.5")
  end

  it "should return default appium version for os_version 6.0.1" do
    obtained_result = get_default_appium_version("6.0.1")
    expect(obtained_result).to eq("1.22.0")
  end
end

describe "install_app" do
  let(:session_id) { "session_id" }
  let(:device_id) { "pixel_3_device_id" }

  before do
    stub_const("CONFIG_FILE", "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    allow(File).to receive(:exist?).and_return(true)
    allow(@mock_logger).to receive(:info)
  end

  it "installs app with hashed_id passed same as duplicate other app" do
    session_file = {
      'downloaded_other_apps_details' => { "hashed_id" => { app_path: "app_path", bundle_id: "bundle_id" } }
    }
    allow(File).to receive("read").and_return(session_file.to_json)

    expect_any_instance_of(Object).to receive(:install_apps).and_return(["success", 0])

    get '/install_app', { device: device_id, automate_session_id: session_id, app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql(200)
  end

  it "does not install app with hashed_id passed when there no duplicate other apps" do
    session_file = {}
    allow(File).to receive("read").and_return(session_file.to_json)

    get '/install_app', { device: device_id, automate_session_id: session_id, app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql(500)
  end

  it "does not install app and does not error with hashed_id passed when there no duplicate other apps but "\
     "present in other apps" do
    session_file = {
      'installed_other_apps_details' => { "hashed_id" => { app_path: "app_path", bundle_id: "bundle_id" } }
    }
    allow(File).to receive("read").and_return(session_file.to_json)

    get '/install_app', { device: device_id, automate_session_id: session_id, app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql(200)
  end

  it "does not install app and errors if signature mismatch is present with hashed_id passed" do
    session_file = {
      'downloaded_other_apps_details' => { "hashed_id" => { app_path: "app_path", bundle_id: "bundle_id" } }
    }
    allow(File).to receive("read").and_return(session_file.to_json)
    expect_any_instance_of(Object).to receive(:install_apps)
      .and_return(["INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES", 1])

    get '/install_app', { device: device_id, automate_session_id: session_id, app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql(500)
  end

  it "does not install app and errors if browserstack error" do
    expect(File).to receive(:exist?).and_return(false)

    get '/install_app', { device: device_id, automate_session_id: session_id, app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql(500)
  end
end

describe "#disable_animation" do
  let(:device_id) { "device1234" }
  let(:success_status) { double("success_exitstatus") }
  let(:failure_status) { double("failure_exitstatus") }

  before(:each) do
    allow(success_status).to receive(:exitstatus).and_return(0)
    allow(failure_status).to receive(:exitstatus).and_return(1)
  end

  it "should return true if command succeeds in first try" do
    expect(OSUtils).to receive(:execute).and_return([nil, success_status])

    result = disable_animation(device_id, "animation_type")

    expect(result).to eq(true)
  end

  it "should return true if command succeeds in second try" do
    expect(OSUtils).to receive(:execute).and_return([nil, failure_status], [nil, success_status])

    result = disable_animation(device_id, "animation_type")

    expect(result).to eq(true)
  end

  it "should return false if command succeeds in both retries" do
    expect(OSUtils).to receive(:execute).and_return([nil, failure_status], [nil, failure_status])

    result = disable_animation(device_id, "animation_type")

    expect(result).to eq(false)
  end
end

describe "install dependent app" do
  let(:dependent_app) do
    [{
      "download_timeout" => "timeout",
      "hashed_id" => "hashed_id_first",
      "download_url" => "url",
      "bundle_id" => "com.example.browserstack_two",
      "zip_align" => "false"
    }, {
      "download_timeout" => "timeout",
      "hashed_id" => "hashed_id_second",
      "download_url" => "url",
      "bundle_id" => "com.example.browserstack_two",
      "zip_align" => "false"
    }]
  end
  let(:params) do
    {
      'app_testing_bundle_id' => 'com.example.browserstack',
      'user_id' => 'user_id',
      "is_minimized_flow" => false,
      :genre => "app_automate"
    }
  end

  let(:minimized_flow_params) do
    {
      'app_testing_bundle_id' => 'com.example.browserstack',
      'user_id' => 'user_id',
      "is_minimized_flow" => true,
      :genre => "app_automate"
    }
  end

  let(:session_id) { "session_id" }
  let(:app_path) { "app_path" }
  let(:bundle_id) { "com.example.browserstack" }
  before do
    allow(@mock_logger).to receive(:info)
  end

  it "installs only non duplicate dependent app", focus: true do
    params['app_testing_bundle_id'] = 'com.example.main_app'
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[0]['bundle_id'],
                                                                              dependent_app[0]["download_url"],
                                                                              false,
                                                                              dependent_app[0]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: false,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_first")
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[1]['bundle_id'],
                                                                              dependent_app[1]["download_url"],
                                                                              false,
                                                                              dependent_app[1]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: true,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_second")
    install_dependent_apps(dependent_app.to_json, session_id, "false", params)
    expect(params['downloaded_other_apps_details']["hashed_id_second"][:app_path]).to eq("app_path_second")
    expect(params['installed_other_apps_details']["hashed_id_first"][:app_path]).to eq("app_path_first")
  end

  it "installs only non duplicate dependent app if subregion proxy enabled", focus: true do
    params['app_testing_bundle_id'] = 'com.example.main_app'
    params["app_automate_custom_params"] = {
      "subregion_app_caching_proxy_enabled" => true
    }
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[0]['bundle_id'],
                                                                              dependent_app[0]["download_url"],
                                                                              false,
                                                                              dependent_app[0]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: false,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: true,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_first")
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[1]['bundle_id'],
                                                                              dependent_app[1]["download_url"],
                                                                              false,
                                                                              dependent_app[1]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: true,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: true,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_second")
    install_dependent_apps(dependent_app.to_json, session_id, "false", params)
    expect(params['downloaded_other_apps_details']["hashed_id_second"][:app_path]).to eq("app_path_second")
    expect(params['installed_other_apps_details']["hashed_id_first"][:app_path]).to eq("app_path_first")
  end

  it "installs only non duplicate dependent app also for minimized flow", focus: true do
    params['app_testing_bundle_id'] = 'com.example.main_app'
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[0]['bundle_id'],
                                                                              dependent_app[0]["download_url"],
                                                                              false,
                                                                              dependent_app[0]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: false,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: true,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              minimized_flow_params["user_id"])
                                                                        .and_return("app_path_first")
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[1]['bundle_id'],
                                                                              dependent_app[1]["download_url"],
                                                                              false,
                                                                              dependent_app[1]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: true,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: true,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              minimized_flow_params["user_id"])
                                                                        .and_return("app_path_second")
    install_dependent_apps(dependent_app.to_json, session_id, "false", minimized_flow_params)
    expect(minimized_flow_params['downloaded_other_apps_details']["hashed_id_second"][:app_path])
      .to eq("app_path_second")
    expect(minimized_flow_params['installed_other_apps_details']["hashed_id_first"][:app_path]).to eq("app_path_first")
  end

  it "does not installs any mid_session dependent app", focus: true do
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[0]['bundle_id'],
                                                                              dependent_app[0]["download_url"],
                                                                              false,
                                                                              dependent_app[0]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: true,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .exactly(2).times.and_return("app_path_first")
    install_dependent_apps(dependent_app.to_json, session_id, "false", params,
                           "mid_session_install_apps")
    expect(params['downloaded_other_apps_details']["hashed_id_first"][:app_path]).to eq("app_path_first")
    expect(params['installed_other_apps_details']).to eq({})
  end

  it "does not installs any other app with bundle id equal to main_app", focus: true do
    dependent_app[0]['bundle_id'] = bundle_id
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[0]['bundle_id'],
                                                                              dependent_app[0]["download_url"],
                                                                              false,
                                                                              dependent_app[0]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: true,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_first")
    expect_any_instance_of(Object).to receive(:download_and_install_app).with(dependent_app[1]['bundle_id'],
                                                                              dependent_app[1]["download_url"],
                                                                              false,
                                                                              dependent_app[1]["download_timeout"],
                                                                              session_id,
                                                                              { app_type: "dependent",
                                                                                zip_align: false,
                                                                                enable_apksigner: "false",
                                                                                skip_install: false,
                                                                                install_experiment_enabled: nil,
                                                                                is_minimized_flow: false,
                                                                                force_resign: "false",
                                                                                subregion_app_caching_enabled: nil,
                                                                                is_app_testing: true },
                                                                              params["user_id"])
                                                                        .and_return("app_path_second")
    install_dependent_apps(dependent_app.to_json, session_id, "false", params)
    expect(params['downloaded_other_apps_details']["hashed_id_first"][:app_path]).to eq("app_path_first")
    expect(params['installed_other_apps_details']["hashed_id_second"][:app_path]).to eq("app_path_second")
  end
end

describe 'kill all apps and restart current app' do
  it "executes all steps successfully if current app is returned" do
    allow_any_instance_of(BrowserStack::AppActions).to receive(:get_current_app_activity).and_return('package/activity')
    allow_any_instance_of(Object).to receive(:script_logger_args).and_return('random_thing')
    expect_any_instance_of(BrowserStack::AppActions).to receive(:kill_all_user_apps).and_return(true)
    expect_any_instance_of(BrowserStack::AppActions).to receive(:start_app).with('package/activity').and_return(true)
    kill_all_apps_and_restart_current_app
  end
  it "short circuits if current app is not found" do
    allow_any_instance_of(BrowserStack::AppActions).to receive(:get_current_app_activity).and_return('')
    kill_all_apps_and_restart_current_app
    expect_any_instance_of(BrowserStack::AppActions).not_to receive(:kill_all_user_apps)
    expect_any_instance_of(BrowserStack::AppActions).not_to receive(:start_app)
  end
end

describe 'verify_google_login' do
  before do
    @mock_android_device = double('AndroidDevice')
    mock_account_helper = double('AccountHelper')
    @exception = GoogleLoginException.new("failed", "google-play-store-login", BROWSERSTACK_ERROR_STRING)
    expect(BrowserStack::AndroidDevice).to receive(:new).and_return(@mock_android_device)

    expect(AccountHelper).to receive(:new).and_return(mock_account_helper)
    expect(mock_account_helper).to receive(:specific_google_account_present?).with("<EMAIL>")
                                                                             .and_return(true)
    allow_any_instance_of(Object).to receive(:zombie_push).and_return(true)
  end

  it 'should call verify_google_login_rooted if device is rooted' do
    allow(@mock_android_device).to receive(:rooted?).and_return(true)
    allow_any_instance_of(Object).to receive(:verify_google_login_rooted).with("<EMAIL>").and_return(true)
    allow_any_instance_of(Object).to receive(:verify_google_login_non_rooted).with("<EMAIL>")
                                                                             .and_raise(@exception)

    expect { verify_google_login("<EMAIL>") }.not_to raise_error
  end

  it 'should call verify_google_login_non_rooted if device is non-rooted' do
    allow(@mock_android_device).to receive(:rooted?).and_return(false)
    allow_any_instance_of(Object).to receive(:verify_google_login_non_rooted).with("<EMAIL>")
                                                                             .and_return(true)
    allow_any_instance_of(Object).to receive(:verify_google_login_rooted).with("<EMAIL>")
                                                                         .and_raise(@exception)

    expect { verify_google_login("<EMAIL>") }.not_to raise_error
  end
end

describe 'verify_google_login_rooted' do
  it 'should start verification and not raise exception if account is logged in' do
    allow_any_instance_of(Object).to receive(:verify_google_login_rooted).with("<EMAIL>").and_return(true)

    expect { verify_google_login_rooted("<EMAIL>") }.not_to raise_error
  end

  it 'should raise exception if bsrun fails' do
    allow(@mock_logger).to receive(:info)
    allow_any_instance_of(Object).to receive(:`).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(1, 0, 0)

    expect { verify_google_login_rooted("<EMAIL>") }.to raise_error(GoogleLoginException, "bsrun failed")
  end

  it 'should raise exception if adb pull fails' do
    allow(@mock_logger).to receive(:info)
    allow_any_instance_of(Object).to receive(:`).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(0, 1, 0)

    expect { verify_google_login_rooted("<EMAIL>") }.to raise_error(
      GoogleLoginException, "adb DB pull failed"
    )
  end

  it 'should raise exception if reading from account DB fails' do
    allow(@mock_logger).to receive(:info)
    allow_any_instance_of(Object).to receive(:`).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(0, 0, 1)

    expect { verify_google_login_rooted("<EMAIL>") }.to raise_error(
      GoogleLoginException, "failed to read DB"
    )
  end

  it 'should raise exception if user is not logged in' do
    allow(@mock_logger).to receive(:info)
    allow_any_instance_of(Object).to receive(:`).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(0, 0, 0)

    expect { verify_google_login_rooted("<EMAIL>") }.to raise_error(
      GoogleLoginException, "user not logged in"
    )
  end
end

describe 'verify_google_login_non_rooted' do
  before do
    @mock_account_helper = double('AccountHelper')
    @exception = GoogleLoginException.new("failed", "google-play-store-login", BROWSERSTACK_ERROR_STRING)
    expect(AccountHelper).to receive(:new).and_return(@mock_account_helper)
  end

  it 'should not raise exception if user is logged in' do
    expect(@mock_account_helper).to receive(:specific_google_account_present?).with("<EMAIL>")
                                                                              .and_return(true)
    expect { verify_google_login_non_rooted("<EMAIL>") }.not_to raise_error
  end

  it 'should raise exception if user is not logged into google' do
    expect(@mock_account_helper).to receive(:specific_google_account_present?).with("<EMAIL>")
                                                                              .and_return(false)
    expect { verify_google_login_non_rooted("<EMAIL>") }
      .to raise_error(GoogleLoginException, "user not logged in")
  end
end

describe 'get_recipient_name_from_email' do
  it 'should return recipient name in case of valid email id' do
    expect(get_recipient_name_from_email("<EMAIL>")).to eq("testuser")
  end

  it 'should return recipient name in case of email id contains `.`' do
    expect(get_recipient_name_from_email("<EMAIL>")).to eq("testuser")
  end

  it 'should return recipient name in case of email id contains multiple `.`' do
    expect(get_recipient_name_from_email("<EMAIL>")).to eq("testuserrandom")
  end

  it 'should return recipient name in case of email id contains `+`' do
    expect(get_recipient_name_from_email("<EMAIL>")).to eq("testuser")
  end

  it 'should return recipient name in case of email id contains `+` and `.`' do
    expect(get_recipient_name_from_email("<EMAIL>")).to eq("testuser")
  end

  it 'should return recipient name in case of email id contains `+` and `.` and does not contain domain' do
    expect(get_recipient_name_from_email("test.user+mydomain")).to eq("testuser")
  end
end

describe "perform_google_login" do
  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    allow_any_instance_of(Object).to receive(:save_logs_params).and_return(true)
    allow_any_instance_of(Object).to receive(:`).and_return("mocked cmd output")
    allow(GooglePlaystoreLoginOutput).to receive(:new).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(0)
    allow_any_instance_of(Object).to receive(:instrument_automation).with("Google_PlayStore_Login_Test",
                                                                          "mocked cmd output", anything)
                                                                    .and_return(true)
  end
  it 'should retry verification if login failed' do
    params = { "app_store_username" => "random_name", "app_store_password" => "random_password", :event_hash => {} }
    first_exception = GoogleLoginException.new("failed to read DB", "google-play-store-login",
                                               BROWSERSTACK_ERROR_STRING)
    second_exception = GoogleLoginException.new("user not logged in", "google-play-store-login",
                                                BROWSERSTACK_ERROR_STRING)
    mock_rooted_android_device = double('AndroidDevice', rooted?: true)
    mock_account_helper = double('AccountHelper', specific_google_account_present?: true)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(mock_rooted_android_device)
    allow(AccountHelper).to receive(:new).and_return(mock_account_helper)
    allow_any_instance_of(Object).to receive(:zombie_push).and_return(true)
    allow(subject).to receive(:verify_google_login).with("random_name").and_raise(first_exception, second_exception)
    expect do
      perform_google_login(params, "device_name", "9")
    end.to raise_error(GoogleLoginException, /#{second_exception.message}/)
  end
  it 'should not retry verification if login failed with any other exception' do
    params = { "app_store_username" => "random_name", "app_store_password" => "random_password", :event_hash => {} }
    first_exception = StandardError.new("random_exception")
    second_exception = GoogleLoginException.new("failed to read DB", "google-play-store-login",
                                                BROWSERSTACK_ERROR_STRING)
    allow(subject).to receive(:verify_google_login).with("random_name").and_raise(first_exception, second_exception)
    expect do
      perform_google_login(params, "device_name", "9")
    end.not_to raise_error(GoogleLoginException, /#{second_exception.message}/)
  end
  it 'should downcase username before calling verify_google_login' do
    params = {
      "app_store_username" => "randomCamelCasedName",
      "app_store_password" => "random_password",
      :event_hash => {}
    }
    allow(@mock_logger).to receive(:info)
    allow_any_instance_of(Object).to receive(:save_logs_params).and_return(true)
    allow_any_instance_of(Object).to receive(:`).and_return("Random test result - OK")
    allow(GooglePlaystoreLoginOutput).to receive(:new).and_return(true)
    allow($CHILD_STATUS).to receive(:exitstatus).and_return(0)
    expect_any_instance_of(Object).to receive(:instrument_automation)
      .with("Google_PlayStore_Login_Test", instance_of(String), anything)
    expect_any_instance_of(Object).to receive(:verify_google_login)
      .with("randomcamelcasedname").and_return(true)
    expect do
      perform_google_login(params, "device", 9)
    end.not_to raise_error
  end
end

describe "Camera Image Injection" do
  let(:device) { 'test_device' }
  let(:session_id) { 'dummy_session_id' }
  let(:media_id) { 'dummy_media_id' }
  let(:format) { '.dummy_format' }
  let(:s3_media_url) { 'dummy_s3_media_url' }
  let(:product) { 'some_product' }
  let(:inject_media_params) do
    {
      "device" => device,
      "file_url" => s3_media_url,
      "media_hashed_id" => media_id,
      "format" => format.downcase,
      "session_id" => session_id,
      "product" => product,
      "no_vpn_device" => false,
      "no_geturl" => false,
      "no_vpn_browser" => false
    }
  end

  describe "/inject_image" do
    before do
      stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
      allow(@mock_logger).to receive(:info)
      allow(@mock_logger).to receive(:error)
      allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
    end

    it "returns 200 when image injection is successful" do
      expect(CameraMediaInjector).to receive(:inject_media).with(device, inject_media_params, push_to_zombies: false)
      expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)
                                                                                 .and_return(true)
      get "/inject_image?device=#{device}&session_id=#{session_id}&file_url=#{s3_media_url}"\
          "&media_hashed_id=#{media_id}&format=#{format}&product=#{product}"
      expect(last_response.status).to eql(200)
    end

    it "returns 500 when image injection fails" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(CameraMediaInjector).to receive(:inject_media).with(device, inject_media_params, push_to_zombies: false)
                                                           .and_raise("dummy_error")
      expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)
                                                                                 .and_return(true)
      get "/inject_image?device=#{device}&session_id=#{session_id}&file_url=#{s3_media_url}"\
          "&media_hashed_id=#{media_id}&format=#{format}&product=#{product}"
      expect(last_response.status).to eql(500)
    end

    it "returns 500 when update_app_patching_data_to_state_file fails" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(CameraMediaInjector).to receive(:inject_media).with(device, inject_media_params, push_to_zombies: false)
      expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).and_raise("dummy_error")
      expect do
        get "/inject_image?device=#{device}&session_id=#{session_id}&file_url=#{s3_media_url}"\
            "&media_hashed_id=#{media_id}&format=#{format}&product=#{product}"
      end.to raise_error("dummy_error")
    end
  end

  context "product=live" do
    let(:product) { "live" }
    let(:media_params) do
      {
        "product" => product,
        "device" => device,
        "media_id" => media_id,
        "format" => format,
        "session_id" => session_id,
        "file_url" => s3_media_url,
        "no_vpn_device" => false,
        "no_geturl" => false,
        "no_vpn_browser" => false
      }
    end

    before do
      stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
      allow(@mock_logger).to receive(:info)
      allow(@mock_logger).to receive(:error)
      allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
      @uri = URI::HTTPS.build(path: '/inject_image', query: URI.encode_www_form(media_params)).request_uri
    end

    it "returns 200 when image injection is successful" do
      expect(LiveMediaInjector).to receive(:inject_media).with(device, "user", media_params)
      expect(AppPatchingUtil).to_not receive(:update_app_patching_data_to_state_file)
      get @uri
      expect(last_response.status).to eql(200)
    end

    it "returns 500 when image injection fails" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(LiveMediaInjector).to receive(:inject_media).with(device, "user", media_params).and_raise("dummy_error")
      expect(AppPatchingUtil).to_not receive(:update_app_patching_data_to_state_file)
      get @uri
      expect(last_response.status).to eql(500)
    end
  end
end

describe 'Audio Injection' do
  let(:device) do
    'test_device'
  end

  let(:session) do
    'test_session_id'
  end

  let(:product) do
    'app_live'
  end

  before(:each) do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return(false)
    allow(AudioInjector).to receive(:log_info)
    allow(AudioInjector).to receive(:log_error)
  end

  describe '#inject_audio' do
    let(:media_id) do
      'test_media_id'
    end

    let(:media_url) do
      'test_media_url'
    end

    let(:format) do
      '.wav'
    end

    context 'when Audio Injection is successfull' do
      before(:each) do
        allow(AudioInjector).to receive(:inject_audio)
      end

      it 'should return 200' do
        get "/inject_audio?device=#{device}&session_id=#{session}&product=#{product}"\
        "&file_url=#{media_url}&media_hashed_id=#{media_id}&format=#{format}"
        expect(last_response.status).to eq(200)
      end
    end

    context 'when Audio Injection fails' do
      context 'when Audio Injection Exception is raised' do
        before(:each) do
          allow(AudioInjector).to receive(:inject_audio).and_raise(AudioInjectionException.new(:AL001, 400))
        end

        it 'should return proper error code, status and message from Audio Injection Exception' do
          get "/inject_audio?device=#{device}&session_id=#{session}&product=#{product}"\
          "&file_url=#{media_url}&media_hashed_id=#{media_id}&format=#{format}"
          expect(last_response.status).to eq(400)
          response_body_code = JSON.parse(last_response.body)["code"]
          response_body_message = JSON.parse(last_response.body)["message"]
          expect(response_body_code).to eq("AL001")
          expect(response_body_message).to eq(AUDIO_INJECTION_ERRORS[:"#{response_body_code}"][:message])
        end
      end
    end
  end

  describe '#play_audio' do
    context 'when Audio Played Successfully' do
      before(:each) do
        allow(AudioInjector).to receive(:play_audio)
      end

      it 'should return 200' do
        get "/play_audio?device=#{device}&session_id=#{session}&product=#{product}"
        expect(last_response.status).to eq(200)
      end
    end

    context 'when failed to Play Audio' do
      context 'when Audio Injection Exception is raised' do
        before(:each) do
          allow(AudioInjector).to receive(:play_audio).and_raise(AudioInjectionException.new(:AL001, 400))
        end

        it 'should return proper error code, status and message from Audio Injection Exception' do
          get "/play_audio?device=#{device}&session_id=#{session}&product=#{product}"
          expect(last_response.status).to eq(400)
          response_body_code = JSON.parse(last_response.body)["code"]
          response_body_message = JSON.parse(last_response.body)["message"]
          expect(response_body_code).to eq("AL001")
          expect(response_body_message).to eq(AUDIO_INJECTION_ERRORS[:"#{response_body_code}"][:message])
        end
      end
    end
  end

  describe '#stop_audio' do
    context 'when Audio Stopped Successfully' do
      before(:each) do
        allow(AudioInjector).to receive(:stop_audio)
      end

      it 'should return 200' do
        get "/stop_audio?device=#{device}&session_id=#{session}&product=#{product}"
        expect(last_response.status).to eq(200)
      end
    end

    context 'when failed to Stop Audio' do
      context 'when Audio Injection Exception is raised' do
        before(:each) do
          allow(AudioInjector).to receive(:stop_audio).and_raise(AudioInjectionException.new(:AL001, 400))
        end

        it 'should return proper error code, status and message from Audio Injection Exception' do
          get "/stop_audio?device=#{device}&session_id=#{session}&product=#{product}"
          expect(last_response.status).to eq(400)
          response_body_code = JSON.parse(last_response.body)["code"]
          response_body_message = JSON.parse(last_response.body)["message"]
          expect(response_body_code).to eq("AL001")
          expect(response_body_message).to eq(AUDIO_INJECTION_ERRORS[:"#{response_body_code}"][:message])
        end
      end
    end
  end
end

describe 'Settings' do
  let(:device) { "test_device" }
  let(:session) { "test_session_id" }
  let(:product) { "app_live" }
  let(:mock_settings_helper) { double(BrowserStack::SettingsHelper) }

  before(:each) do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return(false)
    allow(BrowserStack::SettingsHelper).to receive(:new).and_return(mock_settings_helper)
  end

  describe 'GET /settings' do
    before(:each) do
      allow(mock_settings_helper).to receive(:passcode_enabled?).and_return(true)
      allow(mock_settings_helper).to receive(:fix_rotation_enabled?).and_return(true)
    end

    context 'when settings state is successfully retrived' do
      it 'should respond with status 200 and settings state' do
        get "/settings?device=#{device}&session_id=#{session}&product=#{product}"
        expect(last_response.status).to eq(200)
        response_body = last_response.body
        expect(response_body).to eq({
          device_passcode_toggle: true,
          fix_device_rotation: true
        }.to_json)
      end
    end

    context 'when settings state is not successfully retrieved' do
      before(:each) do
        allow(mock_settings_helper).to receive(:passcode_enabled?).and_raise("Some error")
      end

      it 'should responsd with status other than 200' do
        get "/settings?device=#{device}&session_id=#{session}&product=#{product}"
        expect(last_response.status).to_not eq(200)
      end
    end
  end

  describe 'POST /settings' do
    let(:request_body) { { setting: "device_passcode", value: "enable" } }

    before(:each) do
      allow(mock_settings_helper).to receive(:apply_setting).and_return(true)
    end

    context 'when params are not valid' do
      it 'should respond with status 400' do
        post "/settings?device=#{device}", { value: "disable" }.to_json
        expect(last_response.status).to eq(400)
      end
    end

    context 'when setting is updated successfully' do
      it 'should respond with status 200' do
        post "/settings?device=#{device}", request_body.to_json
        expect(last_response.status).to eq(200)
      end
    end

    context 'when setting is not updated successfully' do
      before(:each) do
        allow(mock_settings_helper).to receive(:apply_setting).and_raise("Some error")
      end

      it 'should response with status other than 200' do
        post "/settings?device=#{device}", request_body.to_json
        expect(last_response.status).to_not eq(200)
      end
    end
  end
end

describe "post update_android_settings" do
  let(:req_body) do
    '{"updateAndroidSettings":["date_time"],
     "date_time":{"date":"2019-01-01",
                  "time":"12:15"
                 },
    "device":"ABCD",
    "device_name":"samsung",
    "product":"app_automate",
    "app_automate_session_id":"1234"
    }'
  end

  let(:json_req_body) do
    JSON.parse(req_body)
  end

  let(:date_time_helper_obj) { double('date_time_helper_obj') }

  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  it 'should return 200 if  successfully' do
    allow(JSON).to receive(:parse).and_return(json_req_body)
    expect(DateTimeHelper).to receive(:new).and_return(date_time_helper_obj)
    expect(date_time_helper_obj).to receive(:change_date_time).with("2019-01-01", "12:15").and_return(true)
    post '/update_android_settings'
    expect(last_response.status).to eq(200)
  end
end

describe "post lighthouse/start_test" do
  let(:req_body) do
    '{"job_id":"t20",
    "device_id":"ABCD",
    "device_name":"samsung",
    "timeout": 600,
    "runs": 2,
    "keep_files": true,
    "job_config":{"job_id":"test",
      "url":"https://airtel.com",
      "region":"eu",
      "lighthouse_options":{
            "output":["json"]
          }
        }
    }'
  end

  let(:json_req_body) do
    JSON.parse(req_body)
  end

  let(:lighthouse) { double('lighthouse') }

  before do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
  end

  it 'should return 200 if lighthouse job is started successfully' do
    allow(JSON).to receive(:parse).and_return(req_body)

    expect(Lighthouse).to receive(:new).with(BrowserStack.logger, json_req_body).and_return(lighthouse)
    expect(Lighthouse).to_not receive(:validate_speedlab_network_profile)

    allow(lighthouse).to receive(:initialize_lighthouse).and_return([200, {}])
    post '/lighthouse/start_test'

    expect(last_response.status).to eq(200)
  end

  it 'should return 200 if it is retry' do
    json_req_body["is_retry"] = true
    allow(JSON).to receive(:parse).and_return(json_req_body)

    expect(Lighthouse).to receive(:new).with(BrowserStack.logger, json_req_body).and_return(lighthouse)
    expect(Lighthouse).to_not receive(:validate_speedlab_network_profile)

    allow(lighthouse).to receive(:initialize_lighthouse).and_return([200, {}])
    post '/lighthouse/start_test'

    expect(last_response.status).to eq(200)
  end
end

describe 'get /media/push_to_device' do
  let(:device) { 'test_device' }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return(false)
    allow(FileInjector).to receive(:inject_file)
    allow(Process).to receive(:fork)
    allow(Process).to receive(:detach)
  end

  it 'should return status 200 when file injection is sucessfull' do
    expect(FileInjector).to receive(:inject_file).and_return(true)

    get "/media/push_to_device?device=#{device}"
    expect(last_response.status).to eq(200)
  end

  it 'should return status 500 when file injection has failed' do
    expect(FileInjector).to receive(:inject_file).and_raise("some error")
    get "/media/push_to_device?device=#{device}"
    expect(last_response.status).to eq(500)
  end

  it 'should fork new process when use_pusher is true' do
    expect(Process).to receive(:fork)
    expect(Process).to receive(:detach)

    get "/media/push_to_device?device=#{device}&use_pusher=true"
    expect(last_response.status).to eq(200)
  end

  it 'should not fork new process when use_pusher is true' do
    expect(FileInjector).to receive(:inject_file).and_return(true)
    expect(Process).to_not receive(:fork)
    expect(Process).to_not receive(:detach)

    get "/media/push_to_device?device=#{device}"
    expect(last_response.status).to eq(200)
  end
end

describe '/execute_adb_command' do
  let(:adb) { double('abd') }
  let(:user_exposed_adb_object) { double('user_exposed_adb_object') }
  before(:each) do
    allow(UserExposedADB).to receive(:new).and_return(user_exposed_adb_object)
    allow(FileUtils).to receive(:touch).and_return(true)
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:error)
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    allow_any_instance_of(Object).to receive(:uses_wifi?).and_return false
  end

  context 'Success scenario' do
    it 'returns 200 response with output' do
      expect(File).to receive(:exist?).twice.and_return(false)
      expect(user_exposed_adb_object).to receive(:run_cmd).and_return("output")
      get "/execute_adb_command?device=pixel_3_device_id&session_id=rails_session_id&command=dumpsys%20gfxinfo%20"\
          "com.android.package&product=app_automate"
      expect(last_response.status).to eq(200)
      expect(last_response.body).to eq("{\"executed_command\":\"adb -s pixel_3_device_id shell dumpsys gfxinfo "\
                                       "com.android.package\",\"output\":\"output\"}")
    end
  end

  context 'Unsuccessful scenario' do
    it 'returns 422 response in case of blacklisted abd commands' do
      expect(File).to receive(:exist?).twice.and_return(false)
      expect(user_exposed_adb_object).to receive(:run_cmd).and_raise(UserADBCommandException.new("[ADB custom "\
                                                    "executor] Blacklisted ADB command #{@command} blocked", 422))
      get "/execute_adb_command?device=pixel_3_device_id&session_id=rails_session_id&command=dumpsys%20gfxinfo%"\
          "20com.android.package%0Adumpsys&product=app_automate"
      expect(last_response.status).to eq(422)
      expect(last_response.body).to eq("{}")
    end

    it 'returns 408 response in case execution times out' do
      expect(File).to receive(:exist?).twice.and_return(false)
      expect(user_exposed_adb_object).to receive(:run_cmd).and_raise(UserADBCommandException.new("[ADB custom "\
                                              "executor] timedout for dumpsys gfxinfo com.android.package", 408))
      get "/execute_adb_command?device=pixel_3_device_id&session_id=rails_session_id&command=dumpsys%20gfxinfo%"\
                                              "20com.android.package&product=app_automate"
      expect(last_response.status).to eq(408)
      expect(last_response.body).to eq("{}")
    end

    it 'returns 500 response in case execution times out' do
      expect(File).to receive(:exist?).twice.and_return(false)
      expect(user_exposed_adb_object).to receive(:run_cmd).and_raise(UserADBCommandException.new("[ADB custom "\
                                                "executor] error for dumpsys gfxinfo com.android.package", 500))
      get "/execute_adb_command?device=pixel_3_device_id&session_id=rails_session_id&command=dumpsys%20gfxinfo%"\
          "20com.android.package&product=app_automate"
      expect(last_response.status).to eq(500)
      expect(last_response.body).to eq("{}")
    end
  end
end

describe "disable_app_profiling?" do
  let(:disable_app_profiling_true) { { "disable_app_profiling" => true } }
  let(:disable_app_profiling_false) { { "disable_app_profiling" => false } }
  let(:params_with_disable_app_profiling) { { "app_automate_custom_params" => disable_app_profiling_true } }
  let(:params_without_disable_app_profiling) { { "app_automate_custom_params" => disable_app_profiling_false } }
  let(:params_without_app_automate_custom_params) { { "app" => "123" } }

  context "when app_automate_custom_params cap is not present" do
    it "return false" do
      expect(disable_app_profiling?(params_without_app_automate_custom_params)).to be(false)
    end
  end

  context "when disable_app_profiling flag is false and app_automate_custom_params cap is present" do
    it "return false" do
      expect(disable_app_profiling?(params_without_disable_app_profiling)).to be(false)
    end
  end

  context "when disable_app_profiling flag is true" do
    it "return true" do
      expect(disable_app_profiling?(params_with_disable_app_profiling)).to be(true)
    end
  end
end

describe "enable browser" do
  let(:instance_obj) { double('mock_browser_helper') }
  let(:logger) { @mock_logger }
  let(:browser_helper_exception) { BrowserHelperError.new('random_string') }
  it "enables the requested browser successfully" do
    expect(BrowserHelper).to receive(:new).with('random_device', 'random_os', logger, 'random_session_id', 'automate')
                                          .and_return(instance_obj)
    expect(instance_obj).to receive(:enable_browser).with('org.mozilla.firefox').and_return(true)
    enable_requested_browser('firefox', 'random_device', 'random_os',
                             'random_session_id', 'automate')
  end
  it "raises exception in case of error" do
    expect(BrowserHelper).to receive(:new).with('random_device', 'random_os', logger, 'random_session_id', 'automate')
                                          .and_return(instance_obj)
    expect(instance_obj).to receive(:enable_browser).with('org.mozilla.firefox')
                                                    .and_raise(BrowserHelperError.new("random string"))
    output = begin
      enable_requested_browser('firefox', 'random_device', 'random_os',
                               'random_session_id', 'automate')
    rescue StandardError
      browser_helper_exception
    end
    expect(output).to eq(browser_helper_exception)
  end
end

describe "save_logs_params_for_async" do
  it "calls write_to_file with correct params for type network when no networkLogsCaptureContent" do
    expect_any_instance_of(Object).to receive(:write_to_file).with(
      "requestfile.json",
      hash_including(captureContentFlag: false)
    ).and_return(true)
    save_logs_params_for_async("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      user_id: "some_user"
    }, "network", "/log/copy/path", "har-file", "requestfile.json")
  end

  it "calls write_to_file with correct params for type network when networkLogsCaptureContent is true" do
    expect_any_instance_of(Object).to receive(:write_to_file).with(
      "requestfile.json",
      hash_including(captureContentFlag: true)
    ).and_return(true)
    save_logs_params_for_async("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      user_id: "some_user",
      networkLogsCaptureContent: true
    }, "network", "/log/copy/path", "har-file", "requestfile.json")
  end

  it "calls write_to_file with correct params for type network when networkLogsCaptureContent is false" do
    expect_any_instance_of(Object).to receive(:write_to_file).with(
      "requestfile.json",
      hash_including(captureContentFlag: false)
    ).and_return(true)
    save_logs_params_for_async("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      user_id: "some_user",
      networkLogsCaptureContent: false
    }, "network", "/log/copy/path", "har-file", "requestfile.json")
  end
end

describe "should_skip_install?" do
  let(:device_obj) { double(BrowserStack::AndroidDevice) }
  let(:androidToolkitMock) { double(AndroidToolkit::ADB) }
  let(:params) { { app_testing_app_version: '1.0.1' } }
  let(:unknown_obj) { double('mock_browser_helper') }

  before(:each) do
    allow(device_obj).to receive(:dedicated_cleanup?).and_return(true)
    allow(device_obj).to receive(:fetch_all_apps).and_return(['com.package'])
    allow(AndroidToolkit::ADB).to receive(:new).and_return(androidToolkitMock)
  end

  it "should return false if error is caught" do
    allow(device_obj).to receive(:dedicate_cleanup).and_raise("Exception")
    expect(should_skip_install?({}, {}, nil, false, '')).to eq(false)
  end

  it "should return true if installed version is same as desired version" do
    allow(androidToolkitMock).to receive(:version_names).and_return(['1.0.1'])
    expect(should_skip_install?(params, {}, device_obj, false, 'com.package')).to eq(true)
  end

  it "should return false if installed version is different from desired version" do
    allow(androidToolkitMock).to receive(:version_names).and_return(['1.0.0'])
    expect(should_skip_install?(params, {}, device_obj, false, 'com.package')).to eq(false)
  end

  it "should return true if :skip_install, :is_minimized_flow is true for non-dedicated devices" do
    allow(device_obj).to receive(:dedicated_cleanup?).and_return(false)
    expect(should_skip_install?(params, { skip_install: true }, device_obj, false, 'com.package')).to eq(true)
    expect(should_skip_install?(params, { is_minimized_flow: true }, device_obj, false, 'com.package')).to eq(true)
  end

  it "should return false if forceReinstall caps is true for dedicated devices" do
    allow(device_obj).to receive(:dedicated_cleanup?).and_return(true)
    params[:forceReinstall] = true
    allow(androidToolkitMock).to receive(:model)
    allow(UnknownAppsChecker).to receive(:new).and_return(unknown_obj)
    allow(unknown_obj).to receive(:uninstall_apps).and_return(true)
    expect(should_skip_install?(params, { skip_install: false }, device_obj, false, 'com.package')).to eq(false)
  end
end

describe "save_logs_params" do
  it "writes correct params for type network when no networkLogsCaptureContent" do
    allow(File).to receive(:open).with("/tmp/networklogs_params_device", "w").and_call_original
    expected_str = "device some_session_id networklogs_aws_key networklogs_aws_secret "\
      "us-east-1 networklogs_aws_bucket app_automate false STANDARD_IA true"
    expect_any_instance_of(IO).to receive(:write).with(expected_str)
    save_logs_params("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      networklogs_aws_storage_class: 'STANDARD_IA',
      zip_nw_logs_android_app_aut: "true",
      user_id: "some_user"
    }, "network")
  end

  it "writes correct params for type network when networkLogsCaptureContent is true" do
    allow(File).to receive(:open).with("/tmp/networklogs_params_device", "w").and_call_original
    expected_str = "device some_session_id networklogs_aws_key networklogs_aws_secret "\
      "us-east-1 networklogs_aws_bucket app_automate true STANDARD_IA true"
    expect_any_instance_of(IO).to receive(:write).with(expected_str)
    save_logs_params("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      networklogs_aws_storage_class: 'STANDARD_IA',
      zip_nw_logs_android_app_aut: "true",
      user_id: "some_user",
      networkLogsCaptureContent: true
    }, "network")
  end

  it "writes correct params for type network when networkLogsCaptureContent is false" do
    allow(File).to receive(:open).with("/tmp/networklogs_params_device", "w").and_call_original
    expected_str = "device some_session_id networklogs_aws_key networklogs_aws_secret "\
      "us-east-1 networklogs_aws_bucket app_automate false STANDARD_IA true"
    expect_any_instance_of(IO).to receive(:write).with(expected_str)
    save_logs_params("device", {
      automate_session_id: "some_session_id",
      genre: 'app_automate',
      networklogs_aws_key: 'networklogs_aws_key',
      networklogs_aws_secret: 'networklogs_aws_secret',
      networklogs_aws_bucket: 'networklogs_aws_bucket',
      networklogs_aws_region: 'us-east-1',
      networklogs_aws_storage_class: 'STANDARD_IA',
      zip_nw_logs_android_app_aut: "true",
      user_id: "some_user",
      networkLogsCaptureContent: false
    }, "network")
  end
end

describe "grant_permissions_for_flutter" do
  let(:adb) { double('abd') }
  before(:each) do
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
  end

  it "calls grant permission for request permissions" do
    expect(adb).to receive(:shell)
      .with("dumpsys package test.app "\
        "|\n  sed -n '/requested permissions:/,/install permissions:/{//!p;}' "\
        "| grep 'android.permission'").and_return("permission1\npermission2")
    expect(adb).to receive(:grant_permission).with("test.app", "permission1").and_return('test')
    expect(adb).to receive(:grant_permission).with("test.app", "permission2").and_return('test')
    grant_permissions_for_flutter('test.app', 1234)
  end

  it "calls grant permission for request permissions and ignores changeable permission error" do
    expect(adb).to receive(:shell)
      .with("dumpsys package test.app "\
        "|\n  sed -n '/requested permissions:/,/install permissions:/{//!p;}' "\
        "| grep 'android.permission'").and_return("permission1\npermission2")
    @exception = AndroidToolkit::ADB::ExecutionError.new("permission is not a changeable permission type")
    expect(adb).to receive(:grant_permission).with("test.app", "permission1").and_raise(@exception)
    expect(adb).to receive(:grant_permission).with("test.app", "permission2").and_return('test')
    grant_permissions_for_flutter('test.app', 1234)
  end
end

describe 'handle_dedicated_device_file' do
  let(:device) { 'test_device' }
  let(:adb) { double('adb') }
  let(:params) { { 'is_dedicated_cloud_session' => nil } }
  before(:each) do
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:fatal)
    stub_const("STATE_FILES_DIR", __dir__)
    allow(AndroidToolkit::ADB).to receive(:new).and_return(adb)
  end

  context 'when user belongs to public cloud' do
    before do
      params['is_dedicated_cloud_session'] = false
    end

    it 'returns false and deletes file if exists in device' do
      expect(adb).to receive(:shell).with("ls /sdcard/private_cloud").and_return("/sdcard/private_cloud")
      expect(adb).to receive(:shell).with("rm /sdcard/private_cloud").and_return("true")
      expect(handle_dedicated_device_file(device, params)).to eql(false)
    end

    it 'returns false and deletes file if exists on host' do
      expect(File).to receive(:exist?).and_return(true).twice
      expect(adb).to receive(:shell).with("ls /sdcard/private_cloud").and_return("")
      expect(handle_dedicated_device_file(device, params)).to eql(false)
    end

    it 'returns true if file does exists in device' do
      expect(adb).to receive(:shell).with("ls /sdcard/private_cloud").and_return("")
      expect(handle_dedicated_device_file(device, params)).to eql(true)
    end

    it 'returns true if file does exists on host' do
      expect(File).to receive(:exist?).and_return(false).twice
      expect(adb).to receive(:shell).with("ls /sdcard/private_cloud").and_return("")
      expect(handle_dedicated_device_file(device, params)).to eql(true)
    end

    it 'returns false and deletes dedicated minified cleanup file if exists on host' do
      dedicated_minified_state_file = "#{STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{device}"
      dedicated_device_file = "#{STATE_FILES_DIR}/dedicated_device_#{device}"
      expect(File).to receive(:exist?).with(dedicated_device_file).and_return(false)
      expect(File).to receive(:exist?).with(dedicated_minified_state_file).and_return(true)
      expect(adb).to receive(:shell).with("ls /sdcard/private_cloud").and_return("")
      expect(handle_dedicated_device_file(device, params)).to eql(false)
    end
  end

  context 'when user belongs to dedicated cloud' do
    before do
      params['is_dedicated_cloud_session'] = true
    end

    it 'returns true and creates file' do
      expect(FileUtils).to receive(:touch).and_return(true)
      expect(adb).to receive(:shell).with("touch /sdcard/private_cloud").and_return(true)
      expect(handle_dedicated_device_file(device, params)).to eql(true)
    end

    it 'creates dedicated minified cleanup file if not exists' do
      params['is_dedicated_cloud_session'] = true
      params["dedicated_minified_cleanup"] = true
      dedicated_minified_state_file = "#{STATE_FILES_DIR}/dedicated_minimized_cleanup_reserved_#{device}"
      dedicated_device_file = "#{STATE_FILES_DIR}/dedicated_device_#{device}"

      expect(File).to receive(:exist?).with(dedicated_device_file).and_return(false)
      expect(FileUtils).to receive(:touch).and_return(true)
      expect(File).to receive(:exist?).with(dedicated_minified_state_file).and_return(false)
      expect(FileUtils).to receive(:touch).with(dedicated_minified_state_file).and_return(true)
      expect(adb).to receive(:shell).with("touch /sdcard/private_cloud").and_return(true)
      expect(handle_dedicated_device_file(device, params)).to eql(true)
    end
  end
end

describe '#start_browser_activity_monitoring' do
  let(:device) { double('device') }
  let(:params) do
    {
      enable_url_detection: 'true',
      live_session_id: 'session123',
      debugger_port: 3000,
      genre: 'test'
    }
  end
  let(:browser_activity_monitoring_instance) { double('BrowserActivityMonitoring') }

  before do
    allow(BrowserStack).to receive_message_chain(:logger, :info)
    allow(BrowserStack).to receive_message_chain(:logger, :error)
  end

  context 'when enable_url_detection is not true' do
    before { params[:enable_url_detection] = 'false' }

    it 'does not start monitoring' do
      expect(BrowserActivityMonitoring).not_to receive(:running?)
      start_browser_activity_monitoring(device, params)
    end
  end

  context 'when enable_url_detection is true' do
    before { params[:enable_url_detection] = 'true' }

    context 'and monitoring is not running' do
      before do
        allow(BrowserActivityMonitoring).to receive(:running?).with(device).and_return(false)
        allow(BrowserActivityMonitoring).to receive(:new).and_return(browser_activity_monitoring_instance)
        allow(browser_activity_monitoring_instance).to receive(:start)
      end

      it 'starts the browser activity monitoring' do
        expect(BrowserActivityMonitoring).to receive(:new).with(
          params[:live_session_id], device, params[:debugger_port], params[:genre]
        ).and_return(browser_activity_monitoring_instance)
        expect(browser_activity_monitoring_instance).to receive(:start)

        start_browser_activity_monitoring(device, params)
      end
    end

    context 'and monitoring is already running' do
      let(:start_file) { double('start_file') }

      before do
        allow(BrowserActivityMonitoring).to receive(:running?).with(device).and_return(true)
        allow(BrowserActivityMonitoring).to receive(:start_file).with(device).and_return(start_file)
        allow(File).to receive(:open).with(start_file, 'w').and_yield(double('file', puts: nil, flock: nil))
      end

      it 'updates the session id of the already running monitoring' do
        expect(File).to receive(:open).with(start_file, 'w')

        start_browser_activity_monitoring(device, params)
      end
    end
  end

  context 'when an exception occurs' do
    let(:start_file) { double('start_file') }

    before do
      allow(BrowserActivityMonitoring).to receive(:running?).with(device).and_return(true)
      allow(BrowserActivityMonitoring).to receive(:start_file).with(device).and_return(start_file)
      allow(File).to receive(:open).with(start_file, 'w').and_raise(StandardError.new('test exception'))
      allow(File).to receive(:delete).with(start_file)
    end

    it 'logs the error and deletes the start file if monitoring is running' do
      expect(BrowserStack.logger).to receive(:error).with(/start_browser_activity_monitoring exception: test exception/)
      expect(File).to receive(:delete).with(start_file)

      start_browser_activity_monitoring(device, params)
    end
  end
end

describe "#start_session_save_polling" do
  let(:device) { "test_device" }
  let(:params) do
    {
      "additional_action" => "enable_cookie_restore",
      "live_session_id" => "test_session_id",
      "pre_signed_url" => "https://example.com/cookies.json",
      "genre" => "app_live",
      "set_cookies" => true
    }
  end
  let(:session_save_polling) { double(SessionSavePolling) }

  before(:each) do
    # This is the key fix - set @device and @devices_json correctly
    @device = device
    @devices_json = { device => { "debugger_port" => 9222 } }

    allow(BrowserStack).to receive_message_chain(:logger, :info)
    allow(BrowserStack).to receive_message_chain(:logger, :error)
    allow(BrowserStack::HttpUtils).to receive(:download_from_s3_with_presigned_url).and_return(true)
    allow(SessionSavePolling).to receive(:new).and_return(session_save_polling)
    allow(session_save_polling).to receive(:start)
    allow(File).to receive(:open).and_yield(double(flock: nil, puts: nil))

    # Mock the start_file method to avoid file system errors
    allow(SessionSavePolling).to receive(:start_file).with(device).and_return("/tmp/session_save_polling_#{device}")
    allow(File).to receive(:delete).with("/tmp/session_save_polling_#{device}")
  end

  context "when additional_action is not enable_cookie_restore" do
    it "returns early without starting polling" do
      params["additional_action"] = "something_else"
      expect(SessionSavePolling).not_to receive(:running?)

      start_session_save_polling(device, params)
    end
  end

  context "when additional_action is enable_cookie_restore" do
    before do
      params[:debugger_port] = nil
    end

    it "sets debugger_port from devices_json" do
      expect { start_session_save_polling(device, params) }.to change { params[:debugger_port] }.from(nil).to(9222)
    end

    it "downloads cookie file from S3 using pre-signed URL" do
      cookie_path = "/usr/local/.browserstack/state_files/#{device}_cookie_data_from_s3.json"
      expect(BrowserStack::HttpUtils).to receive(:download_from_s3_with_presigned_url)
        .with(params[:pre_signed_url], cookie_path)
        .and_return(true)

      start_session_save_polling(device, params)
    end

    context "when polling is not already running" do
      before do
        allow(SessionSavePolling).to receive(:running?).with(device).and_return(false)
      end

      it "creates a new SessionSavePolling instance and starts it" do
        start_session_save_polling(device, params)

        expect(SessionSavePolling).to have_received(:new)
          .with(params[:live_session_id], device, 9222, params[:genre], params[:set_cookies])
        expect(session_save_polling).to have_received(:start)
      end
    end

    context "when polling is already running" do
      before do
        allow(SessionSavePolling).to receive(:running?).with(device).and_return(true)
      end

      it "updates the session ID in the start file" do
        expect(File).to receive(:open).with(SessionSavePolling.start_file(device), 'w')

        start_session_save_polling(device, params)
      end
    end

    context "when an exception occurs" do
      let(:error) { StandardError.new("Test error") }

      before do
        allow(SessionSavePolling).to receive(:running?).with(device).and_return(true)
        allow(File).to receive(:open).and_raise(error)
      end

      it "logs the error and deletes the start file if it exists" do
        error_msg = "[SessionSavePolling] start_session_save_polling exception: Test error"
        expect(BrowserStack.logger).to receive(:error).with(error_msg)
        expect(File).to receive(:delete).with(SessionSavePolling.start_file(device))

        start_session_save_polling(device, params)
      end
    end

    context "when S3 download fails" do
      before do
        allow(BrowserStack::HttpUtils).to receive(:download_from_s3_with_presigned_url).and_return(false)
      end

      it "logs an error but continues with polling setup" do
        error_msg = "[SessionSavePolling] Failed to download cookies file from S3"
        expect(BrowserStack.logger).to receive(:error).with(error_msg)
        expect(SessionSavePolling).to receive(:running?).with(device)

        start_session_save_polling(device, params)
      end
    end
  end
end

describe 'POST /app_live/get_snapshot_details' do
  let(:app_live_session_id) { '12345' }
  let(:session_details_file) { "#{STATE_FILES_DIR}/al_session_#{app_live_session_id}" }
  let(:device) { 'oneplus7_device_id' }
  let(:session_details) { { 'device' => device , 'app_live_session_id' => app_live_session_id }.to_json }
  let(:config_file) { 'path/to/config_file' }
  let(:config) { { 'devices' => { device => { 'device_name' => 'oneplus7_device_id' } } }.to_json }
  let(:snapshot_details) { { 'snapshot' => 'details' } }
  let(:uiautomator2_enabled) { true }

  before do
    allow(self).to receive(:get_session_id_from_params).and_return(app_live_session_id)
    allow(self).to receive(:get_snapshot_details_wrapper).and_return(snapshot_details)
    allow(self).to receive(:start_interactions)
    allow(File).to receive(:exist?).and_call_original
    allow(File).to receive(:read).and_call_original
    allow(BrowserStack.logger).to receive(:error)
    stub_const("CONFIG_FILE", "#{__dir__}/test_config.json")
    stub_const("STATE_FILES_DIR", __dir__.to_s)
  end

  context 'when the session file does not exist' do
    before do
      allow(File).to receive(:exist?).with(session_details_file).and_return(false)
    end

    it 'returns a 404 error' do
      post '/app_live/get_snapshot_details', { app_live_session_id: app_live_session_id }.to_json

      expect(last_response.status).to eq(404)
      expect(JSON.parse(last_response.body)).to eq({ "error" => true, "message" => "No Terminal found" })
    end
  end

  context 'when an exception occurs' do
    let(:error_message) { 'Something went wrong' }

    before do
      allow(File).to receive(:exist?).with(session_details_file).and_raise(StandardError.new(error_message))
      allow(File).to receive(:read).with(session_details_file).and_return(session_details)
    end
    it 'returns a 500 error and logs the exception' do
      post '/app_live/get_snapshot_details', { app_live_session_id: app_live_session_id }.to_json
      expect(last_response.status).to eq(500)
      expect(JSON.parse(last_response.body)).to eq({ "error" => true, "message" => "Error getting snapshot details." })
      expect(BrowserStack.logger)
        .to have_received(:error)
        .with(%r{Exception in /get_snapshot_details : #{error_message}})
    end
  end
end

describe '#execute_app_live_session_restart_action' do
  let(:actions) { [] }
  let(:params) { { "applive_disable_privoxy_for_session" => "false" } }
  let(:device_obj) { double('device_obj') }

  before do
    stub_const('CONFIG_FILE', "#{__dir__}/test_config.json")
    @devices_json = JSON.parse(File.read("#{__dir__}/test_config.json"))['devices']
    @device = 'test_device'
    allow(BrowserStack).to receive(:logger).and_return(double(info: nil, error: nil))
    allow(File).to receive(:read).and_return({ 'network_logs_port' => 9090 }.to_json)
    allow(File).to receive(:open).and_yield(double(write: nil))
    allow(BStackReverseTetherController).to receive_message_chain(:new, :toggle_transparent_mode)
    allow(BrowserStack::AndroidDevice).to receive(:new).and_return(device_obj)
    allow_any_instance_of(BrowserStack::AndroidDevice).to receive(:uses_bstack_internet_app?).and_return(true)
  end

  context 'when action is proxy-setting' do
    let(:actions) { ['proxy-setting'] }

    it 'sets up the tunnel and restarts the app if privoxy is enabled' do
      expect(self).to receive(:tunnel_setup).with(params)
      expect(self).to receive(:kill_all_apps_and_restart_current_app)
      execute_app_live_session_restart_action(params, actions)
    end

    it 'skips tunnel setup if privoxy is disabled' do
      params["applive_disable_privoxy_for_session"] = "true"
      expect(self).not_to receive(:tunnel_setup)
      expect(self).to receive(:kill_all_apps_and_restart_current_app)
      execute_app_live_session_restart_action(params, actions)
    end
  end

  context 'when action is network_config_change' do
    let(:actions) { ['network_config_change'] }

    before do
      allow(self).to receive(:calculate_privoxy_port).and_return(8081)
      allow(self).to receive(:calc_usb_tunnel_ip).and_return('********')
      allow(self).to receive(:tunnel_setup)
    end

    it 'performs network configuration changes if privoxy is enabled' do
      params["host"] = "example.com"
      expect(self).to receive(:calculate_privoxy_port).with(8080)
      expect(self).to receive(:calc_usb_tunnel_ip).with(8080)

      execute_app_live_session_restart_action(params, actions)
    end

    it 'skips network configuration changes if privoxy is disabled' do
      params["applive_disable_privoxy_for_session"] = "true"
      expect(self).not_to receive(:tunnel_setup)
      execute_app_live_session_restart_action(params, actions)
    end
  end

  context 'when action is invalid' do
    let(:actions) { ['invalid_action'] }

    it 'logs an error for an invalid action' do
      expect(BrowserStack.logger).to receive(:error).with("[execute_restart_action] received invalid action_name")
      execute_app_live_session_restart_action(params, actions)
    end
  end

  context 'when an exception occurs' do
    let(:actions) { ['proxy-setting'] }

    it 'logs and raises the exception' do
      allow(self).to receive(:tunnel_setup).and_raise(StandardError.new("Test error"))
      expect(BrowserStack.logger).to receive(:error).with("[execute_restart_action] exception received: Test error")
      expect { execute_app_live_session_restart_action(params, actions) }.to raise_error(StandardError, "Test error")
    end
  end

  context 'when action is reconnect-streaming' do
    let(:actions) { ['reconnect-streaming'] }

    it 'should call reconnect_streaming method' do
      expect(self).to receive(:reconnect_streaming)
      execute_app_live_session_restart_action(params, actions)
    end
  end
end

describe '#execute_restart_action' do
  context 'when action is reconnect-streaming' do
    let(:actions) { ['reconnect-streaming'] }
    let(:params) { {} }

    it 'should call reconnect_streaming method' do
      expect(self).to receive(:reconnect_streaming)
      execute_app_live_session_restart_action(params, actions)
    end
  end
end
